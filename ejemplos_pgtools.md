# 🎯 Automatización PGTools - Ejemplos de Uso

## 📋 Instalación

```bash
pip install playwright
python -m playwright install
```

## 🚀 Ejemplos Básicos

### 1. <PERSON><PERSON><PERSON> simple (con interfaz visible)
```bash
python test_pgtools.py
```

### 2. Buscar raids en coordenadas específicas
```bash
python pgtools_raid_finder.py --lat 40.745276 --lon -74.009244
```

### 3. Buscar Pokémon específico (ej: Mewtwo = ID 150)
```bash
python pgtools_raid_finder.py --lat 40.745276 --lon -74.009244 --pokemon-id 150
```

### 4. <PERSON>car raids de nivel 5 únicamente
```bash
python pgtools_raid_finder.py --lat 40.745276 --lon -74.009244 --raid-level 5
```

### 5. Buscar en radio de 10km
```bash
python pgtools_raid_finder.py --lat 40.745276 --lon -74.009244 --max-distance 10
```

### 6. Eje<PERSON>ar sin interfaz (headless)
```bash
python pgtools_raid_finder.py --lat 40.745276 --lon -74.009244 --headless
```

### 7. Guardar resultados en JSON
```bash
python pgtools_raid_finder.py --lat 40.745276 --lon -74.009244 --output-json raids.json
```

## 🎮 Ejemplos Combinados

### Buscar Rayquaza en 15km
```bash
python pgtools_raid_finder.py \
  --lat 40.745276 \
  --lon -74.009244 \
  --pokemon-id 384 \
  --max-distance 15 \
  --output-json rayquaza_raids.json
```

### Buscar todos los raids nivel 5 cerca
```bash
python pgtools_raid_finder.py \
  --lat 40.745276 \
  --lon -74.009244 \
  --raid-level 5 \
  --max-distance 20 \
  --headless \
  --output-json legendary_raids.json
```

## 📍 Coordenadas Capturadas

Basado en tu grabación de Playwright:
- **Coordenadas usadas**: `40.745276, -74.009244`
- **Ubicación**: Cerca de Nueva York (Manhattan)

## 🔧 Acciones Automatizadas

El script replica exactamente lo que hiciste:

1. ✅ **Navegar a PGTools** - `https://pgtools.net/raids`
2. ✅ **Abrir configuración** - Click en "Configs and Filters"
3. ✅ **Introducir coordenadas** - Campo "Enter your coordinates"
4. ✅ **Activar búsqueda** - Presionar Enter
5. ✅ **Extraer resultados** - Parsear elementos de la página

## 🎯 Parámetros Disponibles

| Parámetro | Descripción | Ejemplo |
|-----------|-------------|---------|
| `--lat` | Latitud (requerido) | `40.745276` |
| `--lon` | Longitud (requerido) | `-74.009244` |
| `--pokemon-id` | ID específico del Pokémon | `150` (Mewtwo) |
| `--raid-level` | Nivel de raid (1-5) | `5` |
| `--max-distance` | Distancia máxima en km | `20.0` |
| `--headless` | Ejecutar sin interfaz | `--headless` |
| `--output-json` | Archivo de salida JSON | `raids.json` |
| `--timeout` | Timeout en ms | `30000` |

## 📊 Formato de Salida

### Consola
```
🎯 Encontrados 5 raids:
================================================================================
 1. 🐾 Mewtwo
    🏛️  Central Park Gym
    📍 40.745276, -74.009244
    📏 2.5 km
    ⏰ 45 minutes left

 2. 🐾 Rayquaza
    🏛️  Times Square Gym
    📍 40.758896, -73.985130
    📏 3.2 km
    ⏰ 1 hour left
```

### JSON
```json
[
  {
    "pokemon_name": "Mewtwo",
    "pokemon_id": 150,
    "raid_level": 5,
    "gym_name": "Central Park Gym",
    "latitude": 40.745276,
    "longitude": -74.009244,
    "distance_km": 2.5,
    "time_left": "45 minutes left"
  }
]
```

## 🔍 Debug y Troubleshooting

### Ver qué está pasando
```bash
# Ejecutar sin headless para ver el navegador
python pgtools_raid_finder.py --lat 40.745276 --lon -74.009244
```

### Tomar screenshots
```bash
# El script test_pgtools.py toma screenshots automáticamente
python test_pgtools.py
```

### Aumentar timeout si es lento
```bash
python pgtools_raid_finder.py --lat 40.745276 --lon -74.009244 --timeout 60000
```

## 🎮 IDs de Pokémon Populares

| ID | Pokémon | Nivel Típico |
|----|---------|--------------|
| 150 | Mewtwo | 5 |
| 382 | Kyogre | 5 |
| 383 | Groudon | 5 |
| 384 | Rayquaza | 5 |
| 483 | Dialga | 5 |
| 484 | Palkia | 5 |
| 643 | Reshiram | 5 |
| 644 | Zekrom | 5 |

## 🚀 Uso Programático

```python
from pgtools_raid_finder import PGToolsRaidFinder

# Crear buscador
finder = PGToolsRaidFinder(headless=True)

# Buscar raids
results = finder.search_raids(
    latitude=40.745276,
    longitude=-74.009244,
    pokemon_id=150,  # Mewtwo
    max_distance=15.0
)

# Procesar resultados
for raid in results:
    print(f"{raid.pokemon_name} en {raid.gym_name} - {raid.distance_km}km")
```

## 📝 Notas Importantes

1. **🌐 Conexión requerida** - Necesita acceso a internet
2. **🎭 Playwright** - Usa navegador real, puede ser detectado
3. **⏱️ Velocidad** - Toma unos segundos por búsqueda
4. **🔧 Selectores** - Pueden cambiar si PGTools actualiza su interfaz
5. **📍 Coordenadas** - Deben ser válidas (lat: -90 a 90, lon: -180 a 180)
