# 🎯 Ejemplos de Uso - Buscador de Raids

## 📋 Instalación

```bash
pip install requests
```

## 🚀 Ejemplos Básicos

### 1. Buscar el raid más cercano (cualquier Pokémon)
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060
```

### 2. <PERSON>car raids de Mewtwo específicamente
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --pokemon-id 150
```

### 3. Buscar raids de nivel 5 únicamente
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --raid-level 5
```

### 4. Buscar raids en un radio de 5km
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --max-distance 5
```

### 5. Mostrar todos los raids encontrados
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --list-all
```

### 6. Guardar resultados en JSON
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --list-all --output-json raids.json
```

## 🎮 Ejemplos Específicos

### Buscar Rayquaza (ID: 384)
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --pokemon-id 384
```

### Buscar raids nivel 4 en 10km
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --raid-level 4 --max-distance 10
```

### Buscar cualquier legendario (nivel 5) cerca
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --raid-level 5 --list-all
```

## 📍 IDs de Pokémon Populares

### Legendarios Nivel 5
- **150**: Mewtwo
- **382**: Kyogre  
- **383**: Groudon
- **384**: Rayquaza
- **483**: Dialga
- **484**: Palkia
- **643**: Reshiram
- **644**: Zekrom

### Nivel 4 Populares
- **68**: Machamp
- **143**: Snorlax
- **248**: Tyranitar
- **306**: Aggron

### Mega Raids
- **3**: Mega Venusaur
- **6**: Mega Charizard
- **9**: Mega Blastoise
- **94**: Mega Gengar

## 🔧 Uso Programático

```python
from raid_finder import RaidFinder

# Crear instancia
finder = RaidFinder()

# Buscar raid más cercano
closest = finder.get_closest_raid(
    lat=40.7128, 
    lon=-74.0060,
    pokemon_id=150,  # Mewtwo
    max_distance=15.0
)

if closest:
    print(f"Raid encontrado: {closest.pokemon_name}")
    print(f"Distancia: {closest.distance_km:.2f}km")
    print(f"Coordenadas: {closest.latitude}, {closest.longitude}")
```

## 📊 Formato de Salida

### Salida Normal
```
🎯 Raid más cercano:
==================================================
🐾 Pokémon: Mewtwo (ID: 150)
⭐ Nivel: 5
💪 CP: 2500
🏛️  Gym: Central Park Gym
📍 Coordenadas: 40.712800, -74.006000
📏 Distancia: 2.45 km
⏰ Termina: 15:30:45
🗺️  Google Maps: https://maps.google.com/maps?q=40.712800,-74.006000
```

### Salida JSON
```json
[
  {
    "pokemon_name": "Mewtwo",
    "pokemon_id": 150,
    "raid_level": 5,
    "cp": 2500,
    "gym_name": "Central Park Gym",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "distance_km": 2.45,
    "end_time": "2024-01-15T15:30:45"
  }
]
```

## 🌍 Coordenadas de Ciudades Populares

### Estados Unidos
- **Nueva York**: 40.7128, -74.0060
- **Los Ángeles**: 34.0522, -118.2437
- **Chicago**: 41.8781, -87.6298
- **San Francisco**: 37.7749, -122.4194

### Europa
- **Londres**: 51.5074, -0.1278
- **París**: 48.8566, 2.3522
- **Madrid**: 40.4168, -3.7038
- **Roma**: 41.9028, 12.4964

### Asia
- **Tokio**: 35.6762, 139.6503
- **Seúl**: 37.5665, 126.9780
- **Singapur**: 1.3521, 103.8198

### América Latina
- **Ciudad de México**: 19.4326, -99.1332
- **Buenos Aires**: -34.6118, -58.3960
- **São Paulo**: -23.5505, -46.6333

## ⚙️ Configuración Avanzada

### Usar con APIs Reales (cuando estén disponibles)
```bash
python raid_finder.py --lat 40.7128 --lon -74.0060 --api pokemap
```

### Combinar múltiples criterios
```bash
python raid_finder.py \
  --lat 40.7128 \
  --lon -74.0060 \
  --raid-level 5 \
  --max-distance 8 \
  --list-all \
  --output-json legendary_raids.json
```

## 🔍 Tips de Uso

1. **Coordenadas precisas**: Usa Google Maps para obtener coordenadas exactas
2. **Radio óptimo**: 10-20km suele ser un buen balance entre cantidad y distancia
3. **Horarios**: Los raids son más frecuentes durante horas pico (12-14h, 17-19h)
4. **Filtros**: Combina pokemon-id y raid-level para búsquedas específicas
5. **JSON**: Usa --output-json para integrar con otras herramientas
