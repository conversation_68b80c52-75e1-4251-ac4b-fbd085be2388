#!/usr/bin/env python3
"""
Buscador de Raids Cercanos para Pokémon GO
Encuentra el raid más cercano basado en coordenadas y criterios específicos
"""

import requests
import json
import math
import argparse
import sys
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class RaidData:
    """Estructura de datos para un raid"""
    gym_id: str
    gym_name: str
    latitude: float
    longitude: float
    pokemon_id: int
    pokemon_name: str
    raid_level: int
    cp: int
    start_time: datetime
    end_time: datetime
    distance_km: float = 0.0
    
    def __str__(self):
        return f"{self.pokemon_name} (ID:{self.pokemon_id}) - <PERSON><PERSON> {self.raid_level} - {self.gym_name} - {self.distance_km:.2f}km"

class RaidFinder:
    """Clase principal para buscar raids"""
    
    def __init__(self):
        # Diccionario de Pokémon por ID (algunos ejemplos principales)
        self.pokemon_dict = {
            # Legendarios Nivel 5
            150: "Mewtwo", 144: "Articuno", 145: "Zapdos", 146: "Moltres",
            249: "Lugia", 250: "Ho-Oh", 382: "Kyogre", 383: "Groudon", 384: "Rayquaza",
            483: "Dialga", 484: "Palkia", 487: "Giratina", 643: "Reshiram", 644: "Zekrom",
            646: "Kyurem", 716: "Xerneas", 717: "Yveltal", 718: "Zygarde",
            
            # Mega Raids Nivel 5
            3: "Venusaur", 6: "Charizard", 9: "Blastoise", 65: "Alakazam", 94: "Gengar",
            115: "Kangaskhan", 127: "Pinsir", 130: "Gyarados", 142: "Aerodactyl",
            
            # Nivel 4
            68: "Machamp", 76: "Golem", 143: "Snorlax", 248: "Tyranitar",
            306: "Aggron", 359: "Absol", 362: "Glalie",
            
            # Nivel 3
            26: "Raichu", 31: "Nidoqueen", 34: "Nidoking", 36: "Clefable",
            38: "Ninetales", 45: "Vileplume", 57: "Primeape", 62: "Poliwrath",
            71: "Victreebel", 78: "Rapidash", 82: "Magneton", 91: "Cloyster",
            103: "Exeggutor", 105: "Marowak", 110: "Weezing", 112: "Rhydon",
            
            # Nivel 1-2
            1: "Bulbasaur", 4: "Charmander", 7: "Squirtle", 25: "Pikachu",
            129: "Magikarp", 133: "Eevee", 147: "Dratini"
        }
        
        # APIs disponibles (algunas requieren claves)
        self.apis = {
            'pokemap': 'https://pokemap.net/api/raids',
            'pogomap': 'https://map.pogodev.org/api/raids',
            'nycpokemap': 'https://nycpokemap.com/api/raids',
            'sgpokemap': 'https://sgpokemap.com/api/raids'
        }
    
    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calcular distancia entre dos coordenadas usando fórmula de Haversine"""
        R = 6371  # Radio de la Tierra en km
        
        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)
        
        a = (math.sin(dlat/2) * math.sin(dlat/2) + 
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) * 
             math.sin(dlon/2) * math.sin(dlon/2))
        
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c
        
        return distance
    
    def get_pokemon_name(self, pokemon_id: int) -> str:
        """Obtener nombre del Pokémon por ID"""
        return self.pokemon_dict.get(pokemon_id, f"Pokemon_{pokemon_id}")
    
    def fetch_raids_from_api(self, api_name: str, lat: float, lon: float, radius: float = 10.0) -> List[RaidData]:
        """Obtener raids desde una API específica"""
        raids = []
        
        try:
            if api_name == 'mock':
                # Datos simulados para testing
                raids = self.generate_mock_raids(lat, lon)
            else:
                # Implementar llamadas a APIs reales
                url = self.apis.get(api_name)
                if not url:
                    logger.error(f"API no encontrada: {api_name}")
                    return []
                
                params = {
                    'lat': lat,
                    'lon': lon,
                    'radius': radius
                }
                
                response = requests.get(url, params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                raids = self.parse_api_response(data, lat, lon)
                
        except requests.RequestException as e:
            logger.error(f"Error conectando a API {api_name}: {e}")
        except Exception as e:
            logger.error(f"Error procesando datos de {api_name}: {e}")
        
        return raids
    
    def generate_mock_raids(self, center_lat: float, center_lon: float) -> List[RaidData]:
        """Generar raids simulados para testing"""
        import random
        
        raids = []
        current_time = datetime.now()
        
        # Generar raids aleatorios en un radio de 20km
        for i in range(15):
            # Coordenadas aleatorias alrededor del centro
            lat_offset = random.uniform(-0.1, 0.1)  # ~11km
            lon_offset = random.uniform(-0.1, 0.1)
            
            raid_lat = center_lat + lat_offset
            raid_lon = center_lon + lon_offset
            
            # Pokémon aleatorio
            pokemon_ids = list(self.pokemon_dict.keys())
            pokemon_id = random.choice(pokemon_ids)
            pokemon_name = self.get_pokemon_name(pokemon_id)
            
            # Nivel basado en el Pokémon
            if pokemon_id in [150, 144, 145, 146, 249, 250, 382, 383, 384]:  # Legendarios
                raid_level = 5
                cp = random.randint(2200, 2800)
            elif pokemon_id in [68, 76, 143, 248]:  # Nivel 4
                raid_level = 4
                cp = random.randint(1800, 2200)
            elif pokemon_id in [26, 31, 34, 36]:  # Nivel 3
                raid_level = 3
                cp = random.randint(1200, 1800)
            else:  # Nivel 1-2
                raid_level = random.choice([1, 2])
                cp = random.randint(500, 1200)
            
            # Tiempos del raid
            start_time = current_time + timedelta(minutes=random.randint(-30, 60))
            end_time = start_time + timedelta(minutes=45)
            
            # Calcular distancia
            distance = self.calculate_distance(center_lat, center_lon, raid_lat, raid_lon)
            
            raid = RaidData(
                gym_id=f"gym_{i}",
                gym_name=f"Gym {i+1}",
                latitude=raid_lat,
                longitude=raid_lon,
                pokemon_id=pokemon_id,
                pokemon_name=pokemon_name,
                raid_level=raid_level,
                cp=cp,
                start_time=start_time,
                end_time=end_time,
                distance_km=distance
            )
            
            raids.append(raid)
        
        return raids
    
    def parse_api_response(self, data: Dict, center_lat: float, center_lon: float) -> List[RaidData]:
        """Parsear respuesta de API real (implementar según formato específico)"""
        raids = []
        
        # Ejemplo de parsing - ajustar según formato de API
        if 'raids' in data:
            for raid_data in data['raids']:
                try:
                    pokemon_id = raid_data.get('pokemon_id', 0)
                    pokemon_name = self.get_pokemon_name(pokemon_id)
                    
                    lat = float(raid_data.get('latitude', 0))
                    lon = float(raid_data.get('longitude', 0))
                    
                    distance = self.calculate_distance(center_lat, center_lon, lat, lon)
                    
                    raid = RaidData(
                        gym_id=raid_data.get('gym_id', ''),
                        gym_name=raid_data.get('gym_name', 'Unknown Gym'),
                        latitude=lat,
                        longitude=lon,
                        pokemon_id=pokemon_id,
                        pokemon_name=pokemon_name,
                        raid_level=raid_data.get('level', 1),
                        cp=raid_data.get('cp', 0),
                        start_time=datetime.fromisoformat(raid_data.get('start_time', datetime.now().isoformat())),
                        end_time=datetime.fromisoformat(raid_data.get('end_time', datetime.now().isoformat())),
                        distance_km=distance
                    )
                    
                    raids.append(raid)
                    
                except Exception as e:
                    logger.warning(f"Error parseando raid: {e}")
                    continue
        
        return raids
    
    def find_raids(self, lat: float, lon: float, pokemon_id: Optional[int] = None, 
                   raid_level: Optional[int] = None, max_distance: float = 20.0,
                   api_source: str = 'mock') -> List[RaidData]:
        """Buscar raids según criterios"""
        
        logger.info(f"Buscando raids en ({lat}, {lon})")
        logger.info(f"Criterios: Pokemon ID={pokemon_id}, Nivel={raid_level}, Distancia máxima={max_distance}km")
        
        # Obtener raids desde API
        all_raids = self.fetch_raids_from_api(api_source, lat, lon, max_distance)
        
        if not all_raids:
            logger.warning("No se encontraron raids")
            return []
        
        # Filtrar raids
        filtered_raids = []
        
        for raid in all_raids:
            # Filtrar por distancia
            if raid.distance_km > max_distance:
                continue
            
            # Filtrar por Pokémon ID
            if pokemon_id and raid.pokemon_id != pokemon_id:
                continue
            
            # Filtrar por nivel de raid
            if raid_level and raid.raid_level != raid_level:
                continue
            
            # Verificar que el raid esté activo
            current_time = datetime.now()
            if raid.end_time < current_time:
                continue
            
            filtered_raids.append(raid)
        
        # Ordenar por distancia
        filtered_raids.sort(key=lambda x: x.distance_km)
        
        logger.info(f"Encontrados {len(filtered_raids)} raids que cumplen criterios")
        return filtered_raids
    
    def get_closest_raid(self, lat: float, lon: float, pokemon_id: Optional[int] = None,
                        raid_level: Optional[int] = None, max_distance: float = 20.0,
                        api_source: str = 'mock') -> Optional[RaidData]:
        """Obtener el raid más cercano"""
        
        raids = self.find_raids(lat, lon, pokemon_id, raid_level, max_distance, api_source)
        
        if raids:
            closest = raids[0]
            logger.info(f"Raid más cercano: {closest}")
            return closest
        else:
            logger.info("No se encontró ningún raid que cumpla los criterios")
            return None

def main():
    parser = argparse.ArgumentParser(description='Buscador de raids cercanos para Pokémon GO')
    parser.add_argument('--lat', type=float, required=True, help='Latitud de tu ubicación')
    parser.add_argument('--lon', type=float, required=True, help='Longitud de tu ubicación')
    parser.add_argument('--pokemon-id', type=int, help='ID del Pokémon específico (ej: 150 para Mewtwo)')
    parser.add_argument('--raid-level', type=int, choices=[1,2,3,4,5], help='Nivel de raid (1-5)')
    parser.add_argument('--max-distance', type=float, default=20.0, help='Distancia máxima en km (default: 20)')
    parser.add_argument('--api', default='mock', choices=['mock', 'pokemap', 'pogomap'], 
                       help='Fuente de datos (default: mock para testing)')
    parser.add_argument('--list-all', action='store_true', help='Mostrar todos los raids encontrados')
    parser.add_argument('--output-json', help='Guardar resultados en archivo JSON')
    
    args = parser.parse_args()
    
    # Validar coordenadas
    if not (-90 <= args.lat <= 90) or not (-180 <= args.lon <= 180):
        logger.error("Coordenadas inválidas")
        sys.exit(1)
    
    finder = RaidFinder()
    
    if args.list_all:
        # Mostrar todos los raids
        raids = finder.find_raids(
            args.lat, args.lon, 
            args.pokemon_id, args.raid_level, 
            args.max_distance, args.api
        )
        
        if raids:
            print(f"\n📍 Raids encontrados cerca de ({args.lat}, {args.lon}):")
            print("=" * 80)
            for i, raid in enumerate(raids, 1):
                print(f"{i:2d}. {raid}")
                print(f"    📍 Coordenadas: {raid.latitude:.6f}, {raid.longitude:.6f}")
                print(f"    ⏰ Termina: {raid.end_time.strftime('%H:%M:%S')}")
                print(f"    💪 CP: {raid.cp}")
                print()
        else:
            print("❌ No se encontraron raids que cumplan los criterios")
    else:
        # Mostrar solo el más cercano
        closest_raid = finder.get_closest_raid(
            args.lat, args.lon,
            args.pokemon_id, args.raid_level,
            args.max_distance, args.api
        )
        
        if closest_raid:
            print(f"\n🎯 Raid más cercano:")
            print("=" * 50)
            print(f"🐾 Pokémon: {closest_raid.pokemon_name} (ID: {closest_raid.pokemon_id})")
            print(f"⭐ Nivel: {closest_raid.raid_level}")
            print(f"💪 CP: {closest_raid.cp}")
            print(f"🏛️  Gym: {closest_raid.gym_name}")
            print(f"📍 Coordenadas: {closest_raid.latitude:.6f}, {closest_raid.longitude:.6f}")
            print(f"📏 Distancia: {closest_raid.distance_km:.2f} km")
            print(f"⏰ Termina: {closest_raid.end_time.strftime('%H:%M:%S')}")
            
            # Generar enlace de Google Maps
            maps_url = f"https://maps.google.com/maps?q={closest_raid.latitude},{closest_raid.longitude}"
            print(f"🗺️  Google Maps: {maps_url}")
        else:
            print("❌ No se encontró ningún raid que cumpla los criterios")
    
    # Guardar en JSON si se especifica
    if args.output_json:
        raids = finder.find_raids(
            args.lat, args.lon,
            args.pokemon_id, args.raid_level,
            args.max_distance, args.api
        )
        
        raids_data = []
        for raid in raids:
            raids_data.append({
                'pokemon_name': raid.pokemon_name,
                'pokemon_id': raid.pokemon_id,
                'raid_level': raid.raid_level,
                'cp': raid.cp,
                'gym_name': raid.gym_name,
                'latitude': raid.latitude,
                'longitude': raid.longitude,
                'distance_km': raid.distance_km,
                'end_time': raid.end_time.isoformat()
            })
        
        with open(args.output_json, 'w') as f:
            json.dump(raids_data, f, indent=2)
        
        print(f"💾 Resultados guardados en: {args.output_json}")

if __name__ == "__main__":
    main()
