#!/bin/bash

# MCP ADB Server Setup Script
# Este script configura el servidor MCP para ADB

set -e

echo "🚀 Configurando MCP ADB Server..."

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para imprimir mensajes
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar si Node.js está instalado
check_nodejs() {
    print_status "Verificando Node.js..."
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js encontrado: $NODE_VERSION"
        
        # Verificar versión mínima (18.0.0)
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR" -lt 18 ]; then
            print_error "Node.js versión 18+ requerida. Versión actual: $NODE_VERSION"
            exit 1
        fi
    else
        print_error "Node.js no encontrado. Por favor instala Node.js 18+ primero."
        echo "Ubuntu/Debian: sudo apt install nodejs npm"
        echo "O descarga desde: https://nodejs.org/"
        exit 1
    fi
}

# Verificar si npm está instalado
check_npm() {
    print_status "Verificando npm..."
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_success "npm encontrado: $NPM_VERSION"
    else
        print_error "npm no encontrado. Por favor instala npm."
        exit 1
    fi
}

# Verificar si ADB está instalado
check_adb() {
    print_status "Verificando ADB..."
    if command -v adb &> /dev/null; then
        ADB_VERSION=$(adb version | head -n1)
        print_success "ADB encontrado: $ADB_VERSION"
    else
        print_warning "ADB no encontrado en PATH."
        echo "Para instalar ADB:"
        echo "Ubuntu/Debian: sudo apt install android-sdk-platform-tools"
        echo "O descarga Android SDK desde: https://developer.android.com/studio/releases/platform-tools"
        echo ""
        read -p "¿Continuar sin ADB? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Instalar dependencias npm
install_dependencies() {
    print_status "Instalando dependencias npm..."
    if npm install; then
        print_success "Dependencias instaladas correctamente"
    else
        print_error "Error instalando dependencias"
        exit 1
    fi
}

# Compilar TypeScript
build_project() {
    print_status "Compilando proyecto TypeScript..."
    if npm run build; then
        print_success "Proyecto compilado correctamente"
    else
        print_error "Error compilando proyecto"
        exit 1
    fi
}

# Verificar dispositivos Android
check_devices() {
    print_status "Verificando dispositivos Android conectados..."
    if command -v adb &> /dev/null; then
        DEVICES=$(adb devices | grep -v "List of devices" | grep -v "^$" | wc -l)
        if [ "$DEVICES" -gt 0 ]; then
            print_success "$DEVICES dispositivo(s) encontrado(s)"
            adb devices
        else
            print_warning "No hay dispositivos conectados"
            echo "Para conectar un dispositivo:"
            echo "1. Habilita 'Opciones de desarrollador' en Android"
            echo "2. Habilita 'Depuración USB'"
            echo "3. Conecta el dispositivo por USB"
            echo "4. Acepta el prompt de autorización"
        fi
    fi
}

# Crear configuración de ejemplo para Claude Desktop
create_config() {
    print_status "Creando configuración de ejemplo..."
    
    CURRENT_DIR=$(pwd)
    CONFIG_FILE="claude_desktop_config.example.json"
    
    # Actualizar la ruta en el archivo de configuración
    sed "s|/home/<USER>/Documents/proyecto_android|$CURRENT_DIR|g" "$CONFIG_FILE" > "claude_desktop_config.json"
    
    print_success "Configuración creada: claude_desktop_config.json"
    echo ""
    echo "Para usar con Claude Desktop:"
    echo "1. Copia el contenido de claude_desktop_config.json"
    echo "2. Pégalo en tu archivo de configuración de Claude Desktop"
    echo "3. Reinicia Claude Desktop"
    echo ""
    echo "Ubicaciones típicas del archivo de configuración:"
    echo "- macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
    echo "- Windows: %APPDATA%\\Claude\\claude_desktop_config.json"
    echo "- Linux: ~/.config/claude/claude_desktop_config.json"
}

# Función principal
main() {
    echo "=================================================="
    echo "🤖 MCP ADB Server - Script de Configuración"
    echo "=================================================="
    echo ""
    
    check_nodejs
    check_npm
    check_adb
    echo ""
    
    install_dependencies
    build_project
    echo ""
    
    check_devices
    echo ""
    
    create_config
    echo ""
    
    print_success "¡Configuración completada!"
    echo ""
    echo "Comandos útiles:"
    echo "  npm run dev     - Ejecutar en modo desarrollo"
    echo "  npm start       - Ejecutar en modo producción"
    echo "  npm run build   - Compilar proyecto"
    echo "  npm test        - Ejecutar pruebas"
    echo ""
    echo "Para probar el servidor:"
    echo "  npm run dev"
    echo ""
    echo "Luego configura Claude Desktop con el archivo claude_desktop_config.json"
}

# Ejecutar función principal
main
