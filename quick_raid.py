#!/usr/bin/env python3
"""
Script rápido para buscar raids - Interfaz simplificada
"""

import sys
from raid_finder import RaidFinder

def main():
    print("🎯 Buscador Rápido de Raids de Pokémon GO")
    print("=" * 50)
    
    # Obtener coordenadas
    try:
        lat = float(input("📍 Ingresa tu latitud: "))
        lon = float(input("📍 Ingresa tu longitud: "))
    except ValueError:
        print("❌ Coordenadas inválidas")
        sys.exit(1)
    
    # Validar coordenadas
    if not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
        print("❌ Coordenadas fuera de rango")
        sys.exit(1)
    
    print(f"\n📍 Ubicación: {lat}, {lon}")
    
    # Opciones de búsqueda
    print("\n🔍 Opciones de búsqueda:")
    print("1. Cualquier raid cercano")
    print("2. Raids de nivel específico")
    print("3. Pokémon específico por ID")
    print("4. Legendarios (nivel 5)")
    print("5. Mega raids")
    
    try:
        opcion = int(input("\nSelecciona una opción (1-5): "))
    except ValueError:
        print("❌ Opción inválida")
        sys.exit(1)
    
    finder = RaidFinder()
    pokemon_id = None
    raid_level = None
    
    if opcion == 1:
        # Cualquier raid
        pass
    elif opcion == 2:
        # Nivel específico
        try:
            raid_level = int(input("⭐ Ingresa el nivel de raid (1-5): "))
            if not (1 <= raid_level <= 5):
                raise ValueError
        except ValueError:
            print("❌ Nivel inválido")
            sys.exit(1)
    elif opcion == 3:
        # Pokémon específico
        print("\n🐾 Algunos IDs populares:")
        print("150 = Mewtwo, 382 = Kyogre, 383 = Groudon, 384 = Rayquaza")
        print("483 = Dialga, 484 = Palkia, 643 = Reshiram, 644 = Zekrom")
        try:
            pokemon_id = int(input("🐾 Ingresa el ID del Pokémon: "))
        except ValueError:
            print("❌ ID inválido")
            sys.exit(1)
    elif opcion == 4:
        # Legendarios
        raid_level = 5
    elif opcion == 5:
        # Mega raids (también nivel 5 generalmente)
        raid_level = 5
        print("🔥 Buscando Mega raids...")
    else:
        print("❌ Opción inválida")
        sys.exit(1)
    
    # Distancia máxima
    try:
        max_distance = float(input(f"\n📏 Distancia máxima en km (default: 20): ") or "20")
    except ValueError:
        max_distance = 20.0
    
    print(f"\n🔍 Buscando raids...")
    print(f"📍 Ubicación: {lat}, {lon}")
    if pokemon_id:
        pokemon_name = finder.get_pokemon_name(pokemon_id)
        print(f"🐾 Pokémon: {pokemon_name} (ID: {pokemon_id})")
    if raid_level:
        print(f"⭐ Nivel: {raid_level}")
    print(f"📏 Radio: {max_distance} km")
    print("-" * 50)
    
    # Buscar raids
    raids = finder.find_raids(lat, lon, pokemon_id, raid_level, max_distance)
    
    if not raids:
        print("❌ No se encontraron raids que cumplan los criterios")
        print("\n💡 Sugerencias:")
        print("- Aumenta el radio de búsqueda")
        print("- Prueba con criterios menos específicos")
        print("- Verifica que las coordenadas sean correctas")
        return
    
    print(f"✅ Encontrados {len(raids)} raids:")
    print("=" * 80)
    
    for i, raid in enumerate(raids[:10], 1):  # Mostrar máximo 10
        print(f"{i:2d}. 🐾 {raid.pokemon_name} (ID:{raid.pokemon_id}) - Nivel {raid.raid_level}")
        print(f"    🏛️  {raid.gym_name}")
        print(f"    📍 {raid.latitude:.6f}, {raid.longitude:.6f}")
        print(f"    📏 {raid.distance_km:.2f} km")
        print(f"    💪 CP: {raid.cp}")
        print(f"    ⏰ Termina: {raid.end_time.strftime('%H:%M:%S')}")
        
        # Google Maps link
        maps_url = f"https://maps.google.com/maps?q={raid.latitude},{raid.longitude}"
        print(f"    🗺️  {maps_url}")
        print()
    
    if len(raids) > 10:
        print(f"... y {len(raids) - 10} raids más")
    
    # Mostrar el más cercano destacado
    closest = raids[0]
    print("🎯 RAID MÁS CERCANO:")
    print("=" * 50)
    print(f"🐾 {closest.pokemon_name} (ID: {closest.pokemon_id})")
    print(f"⭐ Nivel {closest.raid_level} - 💪 CP {closest.cp}")
    print(f"🏛️  {closest.gym_name}")
    print(f"📍 {closest.latitude:.6f}, {closest.longitude:.6f}")
    print(f"📏 {closest.distance_km:.2f} km de distancia")
    print(f"⏰ Termina a las {closest.end_time.strftime('%H:%M:%S')}")
    
    maps_url = f"https://maps.google.com/maps?q={closest.latitude},{closest.longitude}"
    print(f"🗺️  Google Maps: {maps_url}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 ¡Hasta luego!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
