import type { Device, Package } from '../types/adb.js';
export declare class AdbError extends Error {
    code?: number | undefined;
    constructor(message: string, code?: number | undefined);
}
export declare class AdbUtils {
    private static adbPath;
    /**
     * Set custom ADB path if needed
     */
    static setAdbPath(path: string): void;
    /**
     * Execute ADB command and return output
     */
    static executeCommand(args: string[], timeout?: number): Promise<string>;
    /**
     * Check if ADB is available
     */
    static isAdbAvailable(): Promise<boolean>;
    /**
     * Connect to device over WiFi
     */
    static connectWifi(ipAddress: string, port?: number): Promise<string>;
    /**
     * Disconnect from WiFi device
     */
    static disconnectWifi(ipAddress?: string, port?: number): Promise<string>;
    /**
     * Enable WiFi debugging on connected USB device
     */
    static enableWifiDebugging(deviceId?: string, port?: number): Promise<string>;
    /**
     * Get device IP address
     */
    static getDeviceIpAddress(deviceId?: string): Promise<string>;
    /**
     * Get list of connected devices
     */
    static getDevices(): Promise<Device[]>;
    /**
     * Get the first available device or specific device
     */
    static getDevice(deviceId?: string): Promise<Device>;
    /**
     * Execute shell command on device
     */
    static executeShellCommand(command: string, deviceId?: string, timeout?: number): Promise<string>;
    /**
     * Get installed packages on device
     */
    static getPackages(deviceId?: string): Promise<Package[]>;
    /**
     * Install APK on device
     */
    static installApk(apkPath: string, deviceId?: string, options?: {
        replace?: boolean;
        allowDowngrade?: boolean;
        grantPermissions?: boolean;
    }): Promise<string>;
    /**
     * Uninstall package from device
     */
    static uninstallPackage(packageName: string, deviceId?: string): Promise<string>;
    /**
     * Push file to device
     */
    static pushFile(localPath: string, remotePath: string, deviceId?: string): Promise<string>;
    /**
     * Pull file from device
     */
    static pullFile(remotePath: string, localPath: string, deviceId?: string): Promise<string>;
    /**
     * Take screenshot
     */
    static takeScreenshot(outputPath: string, deviceId?: string): Promise<string>;
    /**
     * Get logcat output
     */
    static getLogcat(options?: {
        deviceId?: string;
        tag?: string;
        priority?: string;
        lines?: number;
        clear?: boolean;
    }): Promise<string>;
}
//# sourceMappingURL=adb.d.ts.map