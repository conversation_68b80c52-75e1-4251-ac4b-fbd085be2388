import { z } from 'zod';
export declare const DeviceSchema: z.ZodObject<{
    id: z.ZodString;
    state: z.ZodEnum<["device", "offline", "unauthorized", "bootloader", "recovery"]>;
    product: z.<PERSON>odOptional<z.ZodString>;
    model: z.ZodOptional<z.ZodString>;
    device: z.ZodOptional<z.ZodString>;
    transport_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    state: "device" | "offline" | "unauthorized" | "bootloader" | "recovery";
    device?: string | undefined;
    product?: string | undefined;
    model?: string | undefined;
    transport_id?: string | undefined;
}, {
    id: string;
    state: "device" | "offline" | "unauthorized" | "bootloader" | "recovery";
    device?: string | undefined;
    product?: string | undefined;
    model?: string | undefined;
    transport_id?: string | undefined;
}>;
export type Device = z.infer<typeof DeviceSchema>;
export declare const PackageSchema: z.ZodObject<{
    name: z.ZodString;
    versionCode: z.ZodOptional<z.ZodString>;
    versionName: z.ZodOptional<z.ZodString>;
    enabled: z.ZodOptional<z.ZodBoolean>;
    system: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    name: string;
    versionCode?: string | undefined;
    versionName?: string | undefined;
    enabled?: boolean | undefined;
    system?: boolean | undefined;
}, {
    name: string;
    versionCode?: string | undefined;
    versionName?: string | undefined;
    enabled?: boolean | undefined;
    system?: boolean | undefined;
}>;
export type Package = z.infer<typeof PackageSchema>;
export declare const FileTransferSchema: z.ZodObject<{
    localPath: z.ZodString;
    remotePath: z.ZodString;
    deviceId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    localPath: string;
    remotePath: string;
    deviceId?: string | undefined;
}, {
    localPath: string;
    remotePath: string;
    deviceId?: string | undefined;
}>;
export type FileTransfer = z.infer<typeof FileTransferSchema>;
export declare const ShellCommandSchema: z.ZodObject<{
    command: z.ZodString;
    deviceId: z.ZodOptional<z.ZodString>;
    timeout: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
}, "strip", z.ZodTypeAny, {
    command: string;
    timeout: number;
    deviceId?: string | undefined;
}, {
    command: string;
    deviceId?: string | undefined;
    timeout?: number | undefined;
}>;
export type ShellCommand = z.infer<typeof ShellCommandSchema>;
export declare const LogcatOptionsSchema: z.ZodObject<{
    deviceId: z.ZodOptional<z.ZodString>;
    tag: z.ZodOptional<z.ZodString>;
    priority: z.ZodOptional<z.ZodEnum<["V", "D", "I", "W", "E", "F", "S"]>>;
    lines: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    clear: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
}, "strip", z.ZodTypeAny, {
    lines: number;
    clear: boolean;
    deviceId?: string | undefined;
    tag?: string | undefined;
    priority?: "V" | "D" | "I" | "W" | "E" | "F" | "S" | undefined;
}, {
    deviceId?: string | undefined;
    tag?: string | undefined;
    priority?: "V" | "D" | "I" | "W" | "E" | "F" | "S" | undefined;
    lines?: number | undefined;
    clear?: boolean | undefined;
}>;
export type LogcatOptions = z.infer<typeof LogcatOptionsSchema>;
export declare const ApkInstallSchema: z.ZodObject<{
    apkPath: z.ZodString;
    deviceId: z.ZodOptional<z.ZodString>;
    replace: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    allowDowngrade: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    grantPermissions: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
}, "strip", z.ZodTypeAny, {
    apkPath: string;
    replace: boolean;
    allowDowngrade: boolean;
    grantPermissions: boolean;
    deviceId?: string | undefined;
}, {
    apkPath: string;
    deviceId?: string | undefined;
    replace?: boolean | undefined;
    allowDowngrade?: boolean | undefined;
    grantPermissions?: boolean | undefined;
}>;
export type ApkInstall = z.infer<typeof ApkInstallSchema>;
export declare const ScreenshotOptionsSchema: z.ZodObject<{
    deviceId: z.ZodOptional<z.ZodString>;
    outputPath: z.ZodString;
    format: z.ZodDefault<z.ZodOptional<z.ZodEnum<["png", "jpg"]>>>;
}, "strip", z.ZodTypeAny, {
    outputPath: string;
    format: "png" | "jpg";
    deviceId?: string | undefined;
}, {
    outputPath: string;
    deviceId?: string | undefined;
    format?: "png" | "jpg" | undefined;
}>;
export type ScreenshotOptions = z.infer<typeof ScreenshotOptionsSchema>;
//# sourceMappingURL=adb.d.ts.map