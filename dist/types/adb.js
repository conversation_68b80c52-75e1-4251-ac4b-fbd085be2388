import { z } from 'zod';
// Device information schema
export const DeviceSchema = z.object({
    id: z.string(),
    state: z.enum(['device', 'offline', 'unauthorized', 'bootloader', 'recovery']),
    product: z.string().optional(),
    model: z.string().optional(),
    device: z.string().optional(),
    transport_id: z.string().optional(),
});
// Package information schema
export const PackageSchema = z.object({
    name: z.string(),
    versionCode: z.string().optional(),
    versionName: z.string().optional(),
    enabled: z.boolean().optional(),
    system: z.boolean().optional(),
});
// File transfer schema
export const FileTransferSchema = z.object({
    localPath: z.string(),
    remotePath: z.string(),
    deviceId: z.string().optional(),
});
// Shell command schema
export const ShellCommandSchema = z.object({
    command: z.string(),
    deviceId: z.string().optional(),
    timeout: z.number().optional().default(30000),
});
// Logcat options schema
export const LogcatOptionsSchema = z.object({
    deviceId: z.string().optional(),
    tag: z.string().optional(),
    priority: z.enum(['V', 'D', 'I', 'W', 'E', 'F', 'S']).optional(),
    lines: z.number().optional().default(100),
    clear: z.boolean().optional().default(false),
});
// APK installation schema
export const ApkInstallSchema = z.object({
    apkPath: z.string(),
    deviceId: z.string().optional(),
    replace: z.boolean().optional().default(false),
    allowDowngrade: z.boolean().optional().default(false),
    grantPermissions: z.boolean().optional().default(false),
});
// Screenshot options schema
export const ScreenshotOptionsSchema = z.object({
    deviceId: z.string().optional(),
    outputPath: z.string(),
    format: z.enum(['png', 'jpg']).optional().default('png'),
});
//# sourceMappingURL=adb.js.map