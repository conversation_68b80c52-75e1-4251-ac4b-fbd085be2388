import { z } from 'zod';
import { AdbUtils, AdbError } from '../utils/adb.js';
import { FileTransferSchema, ScreenshotOptionsSchema, LogcatOptionsSchema } from '../types/adb.js';
/**
 * Tool to push files to device
 */
export const pushFileTool = {
    name: 'adb_push_file',
    description: 'Push a file from local machine to Android device',
    inputSchema: {
        type: 'object',
        properties: {
            localPath: {
                type: 'string',
                description: 'Local file path to push',
            },
            remotePath: {
                type: 'string',
                description: 'Remote path on device where file will be stored',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: ['localPath', 'remotePath'],
    },
};
export async function handlePushFile(args) {
    try {
        const { localPath, remotePath, deviceId } = FileTransferSchema.parse(args);
        const result = await AdbUtils.pushFile(localPath, remotePath, deviceId);
        return `File pushed successfully:\nLocal: ${localPath}\nRemote: ${remotePath}\nResult: ${result}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to pull files from device
 */
export const pullFileTool = {
    name: 'adb_pull_file',
    description: 'Pull a file from Android device to local machine',
    inputSchema: {
        type: 'object',
        properties: {
            remotePath: {
                type: 'string',
                description: 'Remote file path on device to pull',
            },
            localPath: {
                type: 'string',
                description: 'Local path where file will be saved',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: ['remotePath', 'localPath'],
    },
};
const PullFileSchema = z.object({
    remotePath: z.string(),
    localPath: z.string(),
    deviceId: z.string().optional(),
});
export async function handlePullFile(args) {
    try {
        const { remotePath, localPath, deviceId } = PullFileSchema.parse(args);
        const result = await AdbUtils.pullFile(remotePath, localPath, deviceId);
        return `File pulled successfully:\nRemote: ${remotePath}\nLocal: ${localPath}\nResult: ${result}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to list files on device
 */
export const listFilesTool = {
    name: 'adb_list_files',
    description: 'List files and directories on the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            path: {
                type: 'string',
                description: 'Path to list (default: /sdcard)',
                default: '/sdcard',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            detailed: {
                type: 'boolean',
                description: 'Show detailed file information (default: false)',
                default: false,
            },
        },
        required: [],
    },
};
const ListFilesSchema = z.object({
    path: z.string().default('/sdcard'),
    deviceId: z.string().optional(),
    detailed: z.boolean().default(false),
});
export async function handleListFiles(args) {
    try {
        const { path, deviceId, detailed } = ListFilesSchema.parse(args);
        const command = detailed ? `ls -la "${path}"` : `ls "${path}"`;
        const output = await AdbUtils.executeShellCommand(command, deviceId);
        if (!output.trim()) {
            return `Directory is empty or does not exist: ${path}`;
        }
        return `Files in ${path}:\n${output}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to take screenshot
 */
export const takeScreenshotTool = {
    name: 'adb_screenshot',
    description: 'Take a screenshot of the Android device screen',
    inputSchema: {
        type: 'object',
        properties: {
            outputPath: {
                type: 'string',
                description: 'Local path where screenshot will be saved',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            format: {
                type: 'string',
                enum: ['png', 'jpg'],
                description: 'Screenshot format (default: png)',
                default: 'png',
            },
        },
        required: ['outputPath'],
    },
};
export async function handleTakeScreenshot(args) {
    try {
        const { outputPath, deviceId, format } = ScreenshotOptionsSchema.parse(args);
        const result = await AdbUtils.takeScreenshot(outputPath, deviceId);
        return `${result}\nFormat: ${format}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to get logcat output
 */
export const getLogcatTool = {
    name: 'adb_logcat',
    description: 'Get logcat output from the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            tag: {
                type: 'string',
                description: 'Filter by specific tag (optional)',
            },
            priority: {
                type: 'string',
                enum: ['V', 'D', 'I', 'W', 'E', 'F', 'S'],
                description: 'Minimum priority level (V=Verbose, D=Debug, I=Info, W=Warning, E=Error, F=Fatal, S=Silent)',
            },
            lines: {
                type: 'number',
                description: 'Number of lines to retrieve (default: 100)',
                default: 100,
            },
            clear: {
                type: 'boolean',
                description: 'Clear logcat buffer instead of reading (default: false)',
                default: false,
            },
        },
        required: [],
    },
};
export async function handleGetLogcat(args) {
    try {
        const { deviceId, tag, priority, lines, clear } = LogcatOptionsSchema.parse(args);
        const logcatOptions = { lines, clear };
        if (deviceId)
            logcatOptions.deviceId = deviceId;
        if (tag)
            logcatOptions.tag = tag;
        if (priority)
            logcatOptions.priority = priority;
        const result = await AdbUtils.getLogcat(logcatOptions);
        if (clear) {
            return result;
        }
        if (!result.trim()) {
            return 'No logcat entries found';
        }
        const lineCount = result.split('\n').length;
        return `Logcat output (${lineCount} lines):\n${result}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to get device storage information
 */
export const getStorageInfoTool = {
    name: 'adb_get_storage_info',
    description: 'Get storage information from the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: [],
    },
};
const GetStorageInfoSchema = z.object({
    deviceId: z.string().optional(),
});
export async function handleGetStorageInfo(args) {
    try {
        const { deviceId } = GetStorageInfoSchema.parse(args);
        const commands = {
            'Internal Storage': 'df -h /data',
            'External Storage': 'df -h /sdcard',
            'System Partition': 'df -h /system',
            'Cache Partition': 'df -h /cache',
            'All Mounted Filesystems': 'df -h',
        };
        const results = [];
        for (const [label, command] of Object.entries(commands)) {
            try {
                const output = await AdbUtils.executeShellCommand(command, deviceId, 10000);
                if (output.trim()) {
                    results.push(`=== ${label} ===\n${output.trim()}\n`);
                }
            }
            catch (error) {
                results.push(`=== ${label} ===\nError getting information\n`);
            }
        }
        return results.join('\n');
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
//# sourceMappingURL=file-tools.js.map