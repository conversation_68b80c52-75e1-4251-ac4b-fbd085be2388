import { z } from 'zod';
import { AdbUtils, AdbError } from '../utils/adb.js';
/**
 * Tool to list all connected Android devices
 */
export const listDevicesTool = {
    name: 'adb_list_devices',
    description: 'List all connected Android devices with their status and properties',
    inputSchema: {
        type: 'object',
        properties: {},
        required: [],
    },
};
export async function handleListDevices() {
    try {
        const devices = await AdbUtils.getDevices();
        if (devices.length === 0) {
            return 'No devices connected';
        }
        const deviceInfo = devices.map(device => {
            const info = [`ID: ${device.id}`, `State: ${device.state}`];
            if (device.product)
                info.push(`Product: ${device.product}`);
            if (device.model)
                info.push(`Model: ${device.model}`);
            if (device.device)
                info.push(`Device: ${device.device}`);
            if (device.transport_id)
                info.push(`Transport ID: ${device.transport_id}`);
            return info.join(', ');
        }).join('\n');
        return `Connected devices (${devices.length}):\n${deviceInfo}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to get device information
 */
export const getDeviceInfoTool = {
    name: 'adb_get_device_info',
    description: 'Get detailed information about a specific device or the first available device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: [],
    },
};
const GetDeviceInfoSchema = z.object({
    deviceId: z.string().optional(),
});
export async function handleGetDeviceInfo(args) {
    try {
        const { deviceId } = GetDeviceInfoSchema.parse(args);
        const device = await AdbUtils.getDevice(deviceId);
        // Get additional device properties
        const androidVersion = await AdbUtils.executeShellCommand('getprop ro.build.version.release', device.id);
        const apiLevel = await AdbUtils.executeShellCommand('getprop ro.build.version.sdk', device.id);
        const manufacturer = await AdbUtils.executeShellCommand('getprop ro.product.manufacturer', device.id);
        const brand = await AdbUtils.executeShellCommand('getprop ro.product.brand', device.id);
        const serialNumber = await AdbUtils.executeShellCommand('getprop ro.serialno', device.id);
        const info = [
            `Device ID: ${device.id}`,
            `State: ${device.state}`,
            `Android Version: ${androidVersion}`,
            `API Level: ${apiLevel}`,
            `Manufacturer: ${manufacturer}`,
            `Brand: ${brand}`,
            `Serial Number: ${serialNumber}`,
        ];
        if (device.product)
            info.push(`Product: ${device.product}`);
        if (device.model)
            info.push(`Model: ${device.model}`);
        if (device.device)
            info.push(`Device: ${device.device}`);
        return info.join('\n');
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to check ADB connection status
 */
export const checkAdbStatusTool = {
    name: 'adb_check_status',
    description: 'Check if ADB is available and working properly',
    inputSchema: {
        type: 'object',
        properties: {},
        required: [],
    },
};
export async function handleCheckAdbStatus() {
    try {
        const isAvailable = await AdbUtils.isAdbAvailable();
        if (!isAvailable) {
            return 'ADB is not available. Please ensure Android SDK is installed and ADB is in your PATH.';
        }
        const version = await AdbUtils.executeCommand(['version']);
        const devices = await AdbUtils.getDevices();
        return [
            'ADB Status: Available',
            `Version: ${version.split('\n')[0]}`,
            `Connected devices: ${devices.length}`,
            `Ready devices: ${devices.filter(d => d.state === 'device').length}`,
        ].join('\n');
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
//# sourceMappingURL=device-tools.js.map