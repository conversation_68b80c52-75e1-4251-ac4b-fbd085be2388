import { z } from 'zod';
import { AdbUtils, AdbError } from '../utils/adb.js';
import { ApkInstallSchema } from '../types/adb.js';
/**
 * Tool to list installed packages
 */
export const listPackagesTool = {
    name: 'adb_list_packages',
    description: 'List installed packages on the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            filter: {
                type: 'string',
                description: 'Filter packages by name (optional)',
            },
            systemApps: {
                type: 'boolean',
                description: 'Include system apps (default: false)',
                default: false,
            },
        },
        required: [],
    },
};
const ListPackagesSchema = z.object({
    deviceId: z.string().optional(),
    filter: z.string().optional(),
    systemApps: z.boolean().optional().default(false),
});
export async function handleListPackages(args) {
    try {
        const { deviceId, filter, systemApps } = ListPackagesSchema.parse(args);
        let command = 'pm list packages';
        if (!systemApps) {
            command += ' -3'; // Third-party packages only
        }
        if (filter) {
            command += ` | grep "${filter}"`;
        }
        const output = await AdbUtils.executeShellCommand(command, deviceId);
        if (!output.trim()) {
            return filter ? `No packages found matching: ${filter}` : 'No packages found';
        }
        // Parse package names
        const packages = output
            .split('\n')
            .map(line => line.replace('package:', '').trim())
            .filter(pkg => pkg.length > 0)
            .sort();
        return `Installed packages (${packages.length}):\n${packages.join('\n')}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to get package information
 */
export const getPackageInfoTool = {
    name: 'adb_get_package_info',
    description: 'Get detailed information about a specific package',
    inputSchema: {
        type: 'object',
        properties: {
            packageName: {
                type: 'string',
                description: 'Package name to get information for',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: ['packageName'],
    },
};
const GetPackageInfoSchema = z.object({
    packageName: z.string(),
    deviceId: z.string().optional(),
});
export async function handleGetPackageInfo(args) {
    try {
        const { packageName, deviceId } = GetPackageInfoSchema.parse(args);
        // Get package dump info
        const dumpOutput = await AdbUtils.executeShellCommand(`dumpsys package ${packageName}`, deviceId, 15000);
        if (dumpOutput.includes('Unable to find package')) {
            return `Package not found: ${packageName}`;
        }
        // Extract key information
        const lines = dumpOutput.split('\n');
        const info = [`Package: ${packageName}`];
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('versionCode=')) {
                info.push(`Version Code: ${trimmed.split('=')[1]?.split(' ')[0]}`);
            }
            else if (trimmed.startsWith('versionName=')) {
                info.push(`Version Name: ${trimmed.split('=')[1]}`);
            }
            else if (trimmed.startsWith('firstInstallTime=')) {
                info.push(`First Install: ${trimmed.split('=')[1]}`);
            }
            else if (trimmed.startsWith('lastUpdateTime=')) {
                info.push(`Last Update: ${trimmed.split('=')[1]}`);
            }
            else if (trimmed.startsWith('codePath=')) {
                info.push(`Code Path: ${trimmed.split('=')[1]}`);
            }
            else if (trimmed.startsWith('targetSdk=')) {
                info.push(`Target SDK: ${trimmed.split('=')[1]}`);
            }
        }
        // Get app state
        try {
            const stateOutput = await AdbUtils.executeShellCommand(`dumpsys activity | grep -A 5 -B 5 "${packageName}"`, deviceId, 10000);
            if (stateOutput.includes(packageName)) {
                info.push('Status: Running');
            }
            else {
                info.push('Status: Not running');
            }
        }
        catch {
            info.push('Status: Unknown');
        }
        return info.join('\n');
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to install APK
 */
export const installApkTool = {
    name: 'adb_install_apk',
    description: 'Install an APK file on the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            apkPath: {
                type: 'string',
                description: 'Path to the APK file to install',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            replace: {
                type: 'boolean',
                description: 'Replace existing application (default: false)',
                default: false,
            },
            allowDowngrade: {
                type: 'boolean',
                description: 'Allow version downgrade (default: false)',
                default: false,
            },
            grantPermissions: {
                type: 'boolean',
                description: 'Grant all runtime permissions (default: false)',
                default: false,
            },
        },
        required: ['apkPath'],
    },
};
export async function handleInstallApk(args) {
    try {
        const { apkPath, deviceId, replace, allowDowngrade, grantPermissions } = ApkInstallSchema.parse(args);
        const result = await AdbUtils.installApk(apkPath, deviceId, {
            replace,
            allowDowngrade,
            grantPermissions,
        });
        if (result.includes('Success')) {
            return `APK installed successfully: ${apkPath}`;
        }
        else {
            return `Installation failed: ${result}`;
        }
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to uninstall package
 */
export const uninstallPackageTool = {
    name: 'adb_uninstall_package',
    description: 'Uninstall a package from the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            packageName: {
                type: 'string',
                description: 'Package name to uninstall',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: ['packageName'],
    },
};
const UninstallPackageSchema = z.object({
    packageName: z.string(),
    deviceId: z.string().optional(),
});
export async function handleUninstallPackage(args) {
    try {
        const { packageName, deviceId } = UninstallPackageSchema.parse(args);
        const result = await AdbUtils.uninstallPackage(packageName, deviceId);
        if (result.includes('Success')) {
            return `Package uninstalled successfully: ${packageName}`;
        }
        else {
            return `Uninstallation failed: ${result}`;
        }
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to start/stop applications
 */
export const controlAppTool = {
    name: 'adb_control_app',
    description: 'Start or stop an application on the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            packageName: {
                type: 'string',
                description: 'Package name to control',
            },
            action: {
                type: 'string',
                enum: ['start', 'stop', 'force-stop'],
                description: 'Action to perform on the application',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            activity: {
                type: 'string',
                description: 'Specific activity to start (only for start action, optional)',
            },
        },
        required: ['packageName', 'action'],
    },
};
const ControlAppSchema = z.object({
    packageName: z.string(),
    action: z.enum(['start', 'stop', 'force-stop']),
    deviceId: z.string().optional(),
    activity: z.string().optional(),
});
export async function handleControlApp(args) {
    try {
        const { packageName, action, deviceId, activity } = ControlAppSchema.parse(args);
        let command;
        switch (action) {
            case 'start':
                if (activity) {
                    command = `am start -n ${packageName}/${activity}`;
                }
                else {
                    command = `monkey -p ${packageName} -c android.intent.category.LAUNCHER 1`;
                }
                break;
            case 'stop':
                command = `am force-stop ${packageName}`;
                break;
            case 'force-stop':
                command = `am kill ${packageName}`;
                break;
        }
        const result = await AdbUtils.executeShellCommand(command, deviceId);
        return `Application ${action} completed for ${packageName}\nOutput: ${result || 'Success'}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
//# sourceMappingURL=app-tools.js.map