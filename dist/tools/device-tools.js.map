{"version": 3, "file": "device-tools.js", "sourceRoot": "", "sources": ["../../src/tools/device-tools.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAS;IACnC,IAAI,EAAE,kBAAkB;IACxB,WAAW,EAAE,qEAAqE;IAClF,WAAW,EAAE;QACX,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE;KACb;CACF,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,iBAAiB;IACrC,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;QAE5C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,sBAAsB,CAAC;QAChC,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACtC,MAAM,IAAI,GAAG,CAAC,OAAO,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YAE5D,IAAI,MAAM,CAAC,OAAO;gBAAE,IAAI,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5D,IAAI,MAAM,CAAC,KAAK;gBAAE,IAAI,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACtD,IAAI,MAAM,CAAC,MAAM;gBAAE,IAAI,CAAC,IAAI,CAAC,WAAW,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YACzD,IAAI,MAAM,CAAC,YAAY;gBAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAE3E,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,OAAO,sBAAsB,OAAO,CAAC,MAAM,OAAO,UAAU,EAAE,CAAC;IACjE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,OAAO,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC;QACnC,CAAC;QACD,OAAO,qBAAqB,KAAK,EAAE,CAAC;IACtC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAS;IACrC,IAAI,EAAE,qBAAqB;IAC3B,WAAW,EAAE,gFAAgF;IAC7F,WAAW,EAAE;QACX,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oEAAoE;aAClF;SACF;QACD,QAAQ,EAAE,EAAE;KACb;CACF,CAAC;AAEF,MAAM,mBAAmB,GAAG,CAAC,CAAC,MAAM,CAAC;IACnC,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAChC,CAAC,CAAC;AAEH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,IAAa;IACrD,IAAI,CAAC;QACH,MAAM,EAAE,QAAQ,EAAE,GAAG,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAElD,mCAAmC;QACnC,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,mBAAmB,CACvD,kCAAkC,EAClC,MAAM,CAAC,EAAE,CACV,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,mBAAmB,CACjD,8BAA8B,EAC9B,MAAM,CAAC,EAAE,CACV,CAAC;QACF,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,mBAAmB,CACrD,iCAAiC,EACjC,MAAM,CAAC,EAAE,CACV,CAAC;QACF,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,mBAAmB,CAC9C,0BAA0B,EAC1B,MAAM,CAAC,EAAE,CACV,CAAC;QACF,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,mBAAmB,CACrD,qBAAqB,EACrB,MAAM,CAAC,EAAE,CACV,CAAC;QAEF,MAAM,IAAI,GAAG;YACX,cAAc,MAAM,CAAC,EAAE,EAAE;YACzB,UAAU,MAAM,CAAC,KAAK,EAAE;YACxB,oBAAoB,cAAc,EAAE;YACpC,cAAc,QAAQ,EAAE;YACxB,iBAAiB,YAAY,EAAE;YAC/B,UAAU,KAAK,EAAE;YACjB,kBAAkB,YAAY,EAAE;SACjC,CAAC;QAEF,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5D,IAAI,MAAM,CAAC,KAAK;YAAE,IAAI,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QACtD,IAAI,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,IAAI,CAAC,WAAW,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAEzD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,OAAO,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC;QACnC,CAAC;QACD,OAAO,qBAAqB,KAAK,EAAE,CAAC;IACtC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAS;IACtC,IAAI,EAAE,kBAAkB;IACxB,WAAW,EAAE,gDAAgD;IAC7D,WAAW,EAAE;QACX,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE;KACb;CACF,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;QAEpD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,uFAAuF,CAAC;QACjG,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAC3D,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;QAE5C,OAAO;YACL,uBAAuB;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YACpC,sBAAsB,OAAO,CAAC,MAAM,EAAE;YACtC,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE;SACrE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,OAAO,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC;QACnC,CAAC;QACD,OAAO,qBAAqB,KAAK,EAAE,CAAC;IACtC,CAAC;AACH,CAAC"}