import { z } from 'zod';
import { AdbUtils, AdbError } from '../utils/adb.js';
import { ShellCommandSchema } from '../types/adb.js';
/**
 * Tool to execute shell commands on Android device
 */
export const executeShellCommandTool = {
    name: 'adb_shell',
    description: 'Execute a shell command on the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            command: {
                type: 'string',
                description: 'Shell command to execute on the device',
            },
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            timeout: {
                type: 'number',
                description: 'Command timeout in milliseconds (default: 30000)',
                default: 30000,
            },
        },
        required: ['command'],
    },
};
export async function handleExecuteShellCommand(args) {
    try {
        const { command, deviceId, timeout } = ShellCommandSchema.parse(args);
        // Security check - prevent dangerous commands
        const dangerousCommands = [
            'rm -rf',
            'format',
            'fastboot',
            'recovery',
            'bootloader',
            'su -c',
            'chmod 777',
        ];
        const lowerCommand = command.toLowerCase();
        for (const dangerous of dangerousCommands) {
            if (lowerCommand.includes(dangerous)) {
                return `Error: Command contains potentially dangerous operation: ${dangerous}`;
            }
        }
        const output = await AdbUtils.executeShellCommand(command, deviceId, timeout);
        if (!output.trim()) {
            return 'Command executed successfully (no output)';
        }
        return `Command output:\n${output}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to get system information
 */
export const getSystemInfoTool = {
    name: 'adb_get_system_info',
    description: 'Get comprehensive system information from the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: [],
    },
};
const GetSystemInfoSchema = z.object({
    deviceId: z.string().optional(),
});
export async function handleGetSystemInfo(args) {
    try {
        const { deviceId } = GetSystemInfoSchema.parse(args);
        const commands = {
            'Android Version': 'getprop ro.build.version.release',
            'API Level': 'getprop ro.build.version.sdk',
            'Build Number': 'getprop ro.build.display.id',
            'Manufacturer': 'getprop ro.product.manufacturer',
            'Brand': 'getprop ro.product.brand',
            'Model': 'getprop ro.product.model',
            'Device': 'getprop ro.product.device',
            'Board': 'getprop ro.product.board',
            'CPU ABI': 'getprop ro.product.cpu.abi',
            'Kernel Version': 'uname -r',
            'Uptime': 'uptime',
            'Memory Info': 'cat /proc/meminfo | head -3',
            'Storage Info': 'df -h /data',
        };
        const results = [];
        for (const [label, command] of Object.entries(commands)) {
            try {
                const output = await AdbUtils.executeShellCommand(command, deviceId, 10000);
                results.push(`${label}: ${output.trim()}`);
            }
            catch (error) {
                results.push(`${label}: Error getting information`);
            }
        }
        return results.join('\n');
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to get running processes
 */
export const getProcessesTool = {
    name: 'adb_get_processes',
    description: 'Get list of running processes on the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            filter: {
                type: 'string',
                description: 'Filter processes by name (optional)',
            },
        },
        required: [],
    },
};
const GetProcessesSchema = z.object({
    deviceId: z.string().optional(),
    filter: z.string().optional(),
});
export async function handleGetProcesses(args) {
    try {
        const { deviceId, filter } = GetProcessesSchema.parse(args);
        let command = 'ps -A';
        if (filter) {
            command += ` | grep "${filter}"`;
        }
        const output = await AdbUtils.executeShellCommand(command, deviceId);
        if (!output.trim()) {
            return filter ? `No processes found matching: ${filter}` : 'No processes found';
        }
        return `Running processes:\n${output}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to get network information
 */
export const getNetworkInfoTool = {
    name: 'adb_get_network_info',
    description: 'Get network configuration and status from the Android device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: [],
    },
};
export async function handleGetNetworkInfo(args) {
    try {
        const { deviceId } = GetSystemInfoSchema.parse(args);
        const commands = {
            'IP Configuration': 'ip addr show',
            'WiFi Status': 'dumpsys wifi | grep "Wi-Fi is"',
            'Network Interfaces': 'cat /proc/net/dev',
            'DNS Servers': 'getprop net.dns1 && getprop net.dns2',
        };
        const results = [];
        for (const [label, command] of Object.entries(commands)) {
            try {
                const output = await AdbUtils.executeShellCommand(command, deviceId, 10000);
                if (output.trim()) {
                    results.push(`=== ${label} ===\n${output.trim()}\n`);
                }
            }
            catch (error) {
                results.push(`=== ${label} ===\nError getting information\n`);
            }
        }
        return results.join('\n');
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
//# sourceMappingURL=shell-tools.js.map