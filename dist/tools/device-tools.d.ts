import { Tool } from '@modelcontextprotocol/sdk/types.js';
/**
 * Tool to list all connected Android devices
 */
export declare const listDevicesTool: Tool;
export declare function handleListDevices(): Promise<string>;
/**
 * Tool to get device information
 */
export declare const getDeviceInfoTool: Tool;
export declare function handleGetDeviceInfo(args: unknown): Promise<string>;
/**
 * Tool to check ADB connection status
 */
export declare const checkAdbStatusTool: Tool;
export declare function handleCheckAdbStatus(): Promise<string>;
//# sourceMappingURL=device-tools.d.ts.map