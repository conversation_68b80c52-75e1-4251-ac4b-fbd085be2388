import { Tool } from '@modelcontextprotocol/sdk/types.js';
/**
 * Tool to push files to device
 */
export declare const pushFileTool: Tool;
export declare function handlePushFile(args: unknown): Promise<string>;
/**
 * Tool to pull files from device
 */
export declare const pullFileTool: Tool;
export declare function handlePullFile(args: unknown): Promise<string>;
/**
 * Tool to list files on device
 */
export declare const listFilesTool: Tool;
export declare function handleListFiles(args: unknown): Promise<string>;
/**
 * Tool to take screenshot
 */
export declare const takeScreenshotTool: Tool;
export declare function handleTakeScreenshot(args: unknown): Promise<string>;
/**
 * Tool to get logcat output
 */
export declare const getLogcatTool: Tool;
export declare function handleGetLogcat(args: unknown): Promise<string>;
/**
 * Tool to get device storage information
 */
export declare const getStorageInfoTool: Tool;
export declare function handleGetStorageInfo(args: unknown): Promise<string>;
//# sourceMappingURL=file-tools.d.ts.map