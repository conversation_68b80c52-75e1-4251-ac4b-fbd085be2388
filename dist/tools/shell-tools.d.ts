import { Tool } from '@modelcontextprotocol/sdk/types.js';
/**
 * Tool to execute shell commands on Android device
 */
export declare const executeShellCommandTool: Tool;
export declare function handleExecuteShellCommand(args: unknown): Promise<string>;
/**
 * Tool to get system information
 */
export declare const getSystemInfoTool: Tool;
export declare function handleGetSystemInfo(args: unknown): Promise<string>;
/**
 * Tool to get running processes
 */
export declare const getProcessesTool: Tool;
export declare function handleGetProcesses(args: unknown): Promise<string>;
/**
 * Tool to get network information
 */
export declare const getNetworkInfoTool: Tool;
export declare function handleGetNetworkInfo(args: unknown): Promise<string>;
//# sourceMappingURL=shell-tools.d.ts.map