import { Tool } from '@modelcontextprotocol/sdk/types.js';
/**
 * Tool to connect to device over WiFi
 */
export declare const connectWifiTool: Tool;
export declare function handleConnectWifi(args: unknown): Promise<string>;
/**
 * Tool to disconnect from WiFi device
 */
export declare const disconnectWifiTool: Tool;
export declare function handleDisconnectWifi(args: unknown): Promise<string>;
/**
 * Tool to enable WiFi debugging on USB connected device
 */
export declare const enableWifiDebuggingTool: Tool;
export declare function handleEnableWifiDebugging(args: unknown): Promise<string>;
/**
 * Tool to get device IP address
 */
export declare const getDeviceIpTool: Tool;
export declare function handleGetDeviceIp(args: unknown): Promise<string>;
/**
 * Tool to setup WiFi debugging (complete workflow)
 */
export declare const setupWifiDebuggingTool: Tool;
export declare function handleSetupWifiDebugging(args: unknown): Promise<string>;
//# sourceMappingURL=wifi-tools.d.ts.map