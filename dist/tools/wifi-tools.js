import { z } from 'zod';
import { AdbUtils, AdbError } from '../utils/adb.js';
/**
 * Tool to connect to device over WiFi
 */
export const connectWifiTool = {
    name: 'adb_connect_wifi',
    description: 'Connect to Android device over WiFi using IP address',
    inputSchema: {
        type: 'object',
        properties: {
            ipAddress: {
                type: 'string',
                description: 'IP address of the Android device',
            },
            port: {
                type: 'number',
                description: 'Port number for ADB connection (default: 5555)',
                default: 5555,
            },
        },
        required: ['ipAddress'],
    },
};
const ConnectWifiSchema = z.object({
    ipAddress: z.string(),
    port: z.number().default(5555),
});
export async function handleConnectWifi(args) {
    try {
        const { ipAddress, port } = ConnectWifiSchema.parse(args);
        const result = await AdbUtils.connectWifi(ipAddress, port);
        return result;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to disconnect from WiFi device
 */
export const disconnectWifiTool = {
    name: 'adb_disconnect_wifi',
    description: 'Disconnect from WiFi device or all WiFi devices',
    inputSchema: {
        type: 'object',
        properties: {
            ipAddress: {
                type: 'string',
                description: 'IP address to disconnect from (optional, disconnects all if not specified)',
            },
            port: {
                type: 'number',
                description: 'Port number (default: 5555)',
                default: 5555,
            },
        },
        required: [],
    },
};
const DisconnectWifiSchema = z.object({
    ipAddress: z.string().optional(),
    port: z.number().default(5555),
});
export async function handleDisconnectWifi(args) {
    try {
        const { ipAddress, port } = DisconnectWifiSchema.parse(args);
        const result = await AdbUtils.disconnectWifi(ipAddress, port);
        return result;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to enable WiFi debugging on USB connected device
 */
export const enableWifiDebuggingTool = {
    name: 'adb_enable_wifi_debugging',
    description: 'Enable WiFi debugging on a USB connected device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            port: {
                type: 'number',
                description: 'Port number for WiFi debugging (default: 5555)',
                default: 5555,
            },
        },
        required: [],
    },
};
const EnableWifiDebuggingSchema = z.object({
    deviceId: z.string().optional(),
    port: z.number().default(5555),
});
export async function handleEnableWifiDebugging(args) {
    try {
        const { deviceId, port } = EnableWifiDebuggingSchema.parse(args);
        const result = await AdbUtils.enableWifiDebugging(deviceId, port);
        return result;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to get device IP address
 */
export const getDeviceIpTool = {
    name: 'adb_get_device_ip',
    description: 'Get the IP address of a connected Android device',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
        },
        required: [],
    },
};
const GetDeviceIpSchema = z.object({
    deviceId: z.string().optional(),
});
export async function handleGetDeviceIp(args) {
    try {
        const { deviceId } = GetDeviceIpSchema.parse(args);
        const ipAddress = await AdbUtils.getDeviceIpAddress(deviceId);
        return `Device IP address: ${ipAddress}`;
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
/**
 * Tool to setup WiFi debugging (complete workflow)
 */
export const setupWifiDebuggingTool = {
    name: 'adb_setup_wifi_debugging',
    description: 'Complete setup for WiFi debugging: enable WiFi debugging and get IP address',
    inputSchema: {
        type: 'object',
        properties: {
            deviceId: {
                type: 'string',
                description: 'Device ID (optional, uses first available device if not specified)',
            },
            port: {
                type: 'number',
                description: 'Port number for WiFi debugging (default: 5555)',
                default: 5555,
            },
        },
        required: [],
    },
};
export async function handleSetupWifiDebugging(args) {
    try {
        const { deviceId, port } = EnableWifiDebuggingSchema.parse(args);
        const results = [];
        // Step 1: Get device IP address
        results.push('Step 1: Getting device IP address...');
        try {
            const ipAddress = await AdbUtils.getDeviceIpAddress(deviceId);
            results.push(`✅ Device IP: ${ipAddress}`);
            // Step 2: Enable WiFi debugging
            results.push('\nStep 2: Enabling WiFi debugging...');
            const enableResult = await AdbUtils.enableWifiDebugging(deviceId, port);
            results.push(`✅ ${enableResult}`);
            // Step 3: Instructions for next steps
            results.push('\nStep 3: Next steps:');
            results.push(`1. Wait 5-10 seconds for device to restart ADB in TCP/IP mode`);
            results.push(`2. Use: adb_connect_wifi with IP: ${ipAddress} and port: ${port}`);
            results.push(`3. You can then disconnect USB cable and use WiFi connection`);
            return results.join('\n');
        }
        catch (error) {
            results.push(`❌ Failed to get IP address: ${error}`);
            results.push('\nAlternative: You can manually find the IP address in device settings:');
            results.push('Settings > About phone > Status > IP address');
            results.push('Or Settings > WiFi > Advanced > IP address');
            // Still try to enable WiFi debugging
            try {
                results.push('\nTrying to enable WiFi debugging anyway...');
                const enableResult = await AdbUtils.enableWifiDebugging(deviceId, port);
                results.push(`✅ ${enableResult}`);
            }
            catch (enableError) {
                results.push(`❌ Failed to enable WiFi debugging: ${enableError}`);
            }
            return results.join('\n');
        }
    }
    catch (error) {
        if (error instanceof AdbError) {
            return `Error: ${error.message}`;
        }
        return `Unexpected error: ${error}`;
    }
}
//# sourceMappingURL=wifi-tools.js.map