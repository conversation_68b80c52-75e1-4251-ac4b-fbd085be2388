import { Tool } from '@modelcontextprotocol/sdk/types.js';
/**
 * Tool to list installed packages
 */
export declare const listPackagesTool: Tool;
export declare function handleListPackages(args: unknown): Promise<string>;
/**
 * Tool to get package information
 */
export declare const getPackageInfoTool: Tool;
export declare function handleGetPackageInfo(args: unknown): Promise<string>;
/**
 * Tool to install APK
 */
export declare const installApkTool: Tool;
export declare function handleInstallApk(args: unknown): Promise<string>;
/**
 * Tool to uninstall package
 */
export declare const uninstallPackageTool: Tool;
export declare function handleUninstallPackage(args: unknown): Promise<string>;
/**
 * Tool to start/stop applications
 */
export declare const controlAppTool: Tool;
export declare function handleControlApp(args: unknown): Promise<string>;
//# sourceMappingURL=app-tools.d.ts.map