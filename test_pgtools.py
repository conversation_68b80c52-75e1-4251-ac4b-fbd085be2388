#!/usr/bin/env python3
"""
Script de prueba simple para PGTools
Basado en las acciones que grabaste
"""

from playwright.sync_api import sync_playwright
import time

def test_pgtools_search(lat: float, lon: float):
    """Probar búsqueda básica en PGTools"""
    
    with sync_playwright() as p:
        # Abrir navegador (visible para ver qué pasa)
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()
        
        try:
            print(f"🌐 Navegando a PGTools...")
            page.goto("https://pgtools.net/raids")
            
            print("⏳ Esperando que cargue la página...")
            page.wait_for_load_state("networkidle")
            
            print("🔧 Abriendo configuración...")
            page.get_by_role("button", name="right Configs and Filters").click()
            
            print(f"📍 Introduciendo coordenadas: {lat}, {lon}")
            coordinates = f"{lat},{lon}"
            
            coord_input = page.get_by_role("textbox", name="Enter your coordinates")
            coord_input.click()
            coord_input.clear()
            coord_input.fill(coordinates)
            coord_input.press("Enter")
            
            print("🔍 Buscando raids...")
            time.sleep(5)  # Esperar a que procese
            
            print("📊 Intentando extraer resultados...")
            
            # Tomar screenshot para debug
            page.screenshot(path="pgtools_results.png")
            print("📸 Screenshot guardado como pgtools_results.png")
            
            # Intentar extraer algunos elementos
            try:
                # Buscar elementos de raids (ajustar según lo que veas en la página)
                raid_elements = page.locator("[data-testid*='raid'], .raid, .pokemon").all()
                print(f"🎯 Encontrados {len(raid_elements)} elementos de raid")
                
                for i, element in enumerate(raid_elements[:5]):  # Solo primeros 5
                    try:
                        text = element.text_content()
                        print(f"  {i+1}. {text[:100]}...")
                    except:
                        print(f"  {i+1}. (No se pudo extraer texto)")
                        
            except Exception as e:
                print(f"⚠️  Error extrayendo elementos: {e}")
            
            print("✅ Prueba completada. Revisa el screenshot para ver los resultados.")
            
            # Mantener abierto por un momento para inspeccionar
            input("Presiona Enter para cerrar el navegador...")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            page.screenshot(path="pgtools_error.png")
            
        finally:
            context.close()
            browser.close()

if __name__ == "__main__":
    # Usar las coordenadas que probaste
    test_pgtools_search(40.745276, -74.009244)
