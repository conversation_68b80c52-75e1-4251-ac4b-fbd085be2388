# MCP ADB Server

Un servidor MCP (Model Context Protocol) para integración con Android Debug Bridge (ADB), que permite controlar dispositivos Android de forma remota a través de herramientas de IA.

## Características

### 🔧 Gestión de Dispositivos
- Listar dispositivos conectados
- Obtener información detallada del dispositivo
- Verificar estado de conexión ADB

### 📱 Gestión de Aplicaciones
- Listar paquetes instalados
- Obtener información de paquetes
- Instalar/desinstalar APKs
- Controlar aplicaciones (iniciar/detener)

### 🖥️ Comandos del Sistema
- Ejecutar comandos shell
- Obtener información del sistema
- Listar procesos en ejecución
- Información de red

### 📁 Gestión de Archivos
- Transferir archivos (push/pull)
- Listar archivos y directorios
- Información de almacenamiento

### 📸 Herramientas Multimedia
- Capturar screenshots
- Obtener logs (logcat)

## Requisitos Previos

1. **Node.js 18+** instalado
2. **Android SDK** con ADB en el PATH
3. **Dispositivo Android** con USB debugging habilitado

### Instalación de Dependencias

```bash
# Instalar Node.js (Ubuntu/Debian)
sudo apt update
sudo apt install nodejs npm

# Verificar instalación
node --version
npm --version
```

### Configuración de ADB

```bash
# Instalar Android SDK tools
sudo apt install android-sdk-platform-tools

# O descargar desde: https://developer.android.com/studio/releases/platform-tools
# Agregar al PATH en ~/.bashrc:
export PATH=$PATH:/path/to/android-sdk/platform-tools

# Verificar ADB
adb version
```

## Instalación

```bash
# Clonar o descargar el proyecto
cd proyecto_android

# Instalar dependencias
npm install

# Compilar TypeScript
npm run build
```

## Uso

### Iniciar el Servidor MCP

```bash
# Modo desarrollo (con recarga automática)
npm run dev

# Modo producción
npm start
```

### Configuración en Claude Desktop

Agregar al archivo de configuración de Claude Desktop (`claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "adb": {
      "command": "node",
      "args": ["/ruta/completa/al/proyecto/dist/index.js"],
      "env": {
        "PATH": "/usr/bin:/bin:/usr/local/bin:/path/to/android-sdk/platform-tools"
      }
    }
  }
}
```

## Herramientas Disponibles

### Gestión de Dispositivos

#### `adb_list_devices`
Lista todos los dispositivos Android conectados.

#### `adb_get_device_info`
Obtiene información detallada de un dispositivo específico.
- `deviceId` (opcional): ID del dispositivo

#### `adb_check_status`
Verifica el estado de ADB y dispositivos conectados.

### Comandos del Sistema

#### `adb_shell`
Ejecuta comandos shell en el dispositivo.
- `command` (requerido): Comando a ejecutar
- `deviceId` (opcional): ID del dispositivo
- `timeout` (opcional): Timeout en ms (default: 30000)

#### `adb_get_system_info`
Obtiene información completa del sistema.
- `deviceId` (opcional): ID del dispositivo

#### `adb_get_processes`
Lista procesos en ejecución.
- `deviceId` (opcional): ID del dispositivo
- `filter` (opcional): Filtrar por nombre

#### `adb_get_network_info`
Obtiene información de red del dispositivo.
- `deviceId` (opcional): ID del dispositivo

### Gestión de Aplicaciones

#### `adb_list_packages`
Lista paquetes instalados.
- `deviceId` (opcional): ID del dispositivo
- `filter` (opcional): Filtrar por nombre
- `systemApps` (opcional): Incluir apps del sistema (default: false)

#### `adb_get_package_info`
Obtiene información detallada de un paquete.
- `packageName` (requerido): Nombre del paquete
- `deviceId` (opcional): ID del dispositivo

#### `adb_install_apk`
Instala un archivo APK.
- `apkPath` (requerido): Ruta al archivo APK
- `deviceId` (opcional): ID del dispositivo
- `replace` (opcional): Reemplazar app existente (default: false)
- `allowDowngrade` (opcional): Permitir downgrade (default: false)
- `grantPermissions` (opcional): Otorgar permisos (default: false)

#### `adb_uninstall_package`
Desinstala un paquete.
- `packageName` (requerido): Nombre del paquete
- `deviceId` (opcional): ID del dispositivo

#### `adb_control_app`
Controla aplicaciones (iniciar/detener).
- `packageName` (requerido): Nombre del paquete
- `action` (requerido): 'start', 'stop', 'force-stop'
- `deviceId` (opcional): ID del dispositivo
- `activity` (opcional): Actividad específica para iniciar

### Gestión de Archivos

#### `adb_push_file`
Transfiere archivo al dispositivo.
- `localPath` (requerido): Ruta local del archivo
- `remotePath` (requerido): Ruta destino en el dispositivo
- `deviceId` (opcional): ID del dispositivo

#### `adb_pull_file`
Descarga archivo del dispositivo.
- `remotePath` (requerido): Ruta del archivo en el dispositivo
- `localPath` (requerido): Ruta destino local
- `deviceId` (opcional): ID del dispositivo

#### `adb_list_files`
Lista archivos y directorios.
- `path` (opcional): Ruta a listar (default: /sdcard)
- `deviceId` (opcional): ID del dispositivo
- `detailed` (opcional): Información detallada (default: false)

#### `adb_get_storage_info`
Obtiene información de almacenamiento.
- `deviceId` (opcional): ID del dispositivo

### Herramientas Multimedia

#### `adb_screenshot`
Captura screenshot del dispositivo.
- `outputPath` (requerido): Ruta donde guardar la imagen
- `deviceId` (opcional): ID del dispositivo
- `format` (opcional): 'png' o 'jpg' (default: png)

#### `adb_logcat`
Obtiene logs del dispositivo.
- `deviceId` (opcional): ID del dispositivo
- `tag` (opcional): Filtrar por tag
- `priority` (opcional): Nivel mínimo (V, D, I, W, E, F, S)
- `lines` (opcional): Número de líneas (default: 100)
- `clear` (opcional): Limpiar buffer (default: false)

## Ejemplos de Uso

### Conectar y verificar dispositivo
```
Usar herramienta: adb_check_status
Usar herramienta: adb_list_devices
```

### Obtener información del sistema
```
Usar herramienta: adb_get_system_info
```

### Instalar una aplicación
```
Usar herramienta: adb_install_apk
Parámetros: {"apkPath": "/ruta/a/mi-app.apk", "replace": true}
```

### Capturar screenshot
```
Usar herramienta: adb_screenshot
Parámetros: {"outputPath": "./screenshot.png"}
```

### Ejecutar comando personalizado
```
Usar herramienta: adb_shell
Parámetros: {"command": "getprop ro.build.version.release"}
```

## Seguridad

- Validación de entrada para prevenir inyección de comandos
- Sanitización de argumentos
- Restricciones en comandos peligrosos
- Validación de rutas para prevenir path traversal

## Desarrollo

```bash
# Instalar dependencias de desarrollo
npm install

# Ejecutar en modo desarrollo
npm run dev

# Compilar
npm run build

# Linting
npm run lint

# Formatear código
npm run format
```

## Solución de Problemas

### ADB no encontrado
```bash
# Verificar que ADB esté en el PATH
which adb
adb version

# Si no está instalado:
sudo apt install android-sdk-platform-tools
```

### Dispositivo no autorizado
1. Verificar que USB debugging esté habilitado
2. Aceptar el prompt de autorización en el dispositivo
3. Verificar con: `adb devices`

### Permisos denegados
- Verificar permisos de archivos
- Usar rutas accesibles como `/sdcard/`
- Verificar que la aplicación tenga permisos necesarios

## Licencia

MIT License
