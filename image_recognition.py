#!/usr/bin/env python3
"""
Módulo de reconocimiento de imágenes para Pokémon GO
Detecta elementos de la UI y estados del juego
"""

import cv2
import numpy as np
from PIL import Image
import logging
from typing import Tuple, List, Optional, Dict
from pathlib import Path

logger = logging.getLogger(__name__)

class PokemonImageRecognition:
    """Clase para reconocimiento de imágenes en Pokémon GO"""
    
    def __init__(self):
        self.templates_path = Path("templates")
        self.templates_path.mkdir(exist_ok=True)
        self.templates = {}
        self.load_templates()
    
    def load_templates(self):
        """Cargar plantillas de imágenes para reconocimiento"""
        template_files = {
            'raid_button': 'raid_button.png',
            'battle_button': 'battle_button.png',
            'pokeball': 'pokeball.png',
            'premier_ball': 'premier_ball.png',
            'catch_success': 'catch_success.png',
            'raid_boss': 'raid_boss.png',
            'gym_icon': 'gym_icon.png',
            'loading': 'loading.png'
        }
        
        for name, filename in template_files.items():
            template_path = self.templates_path / filename
            if template_path.exists():
                self.templates[name] = cv2.imread(str(template_path), cv2.IMREAD_COLOR)
                logger.debug(f"Plantilla cargada: {name}")
            else:
                logger.warning(f"Plantilla no encontrada: {template_path}")
    
    def find_template(self, screenshot_path: str, template_name: str, threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """Buscar plantilla en screenshot"""
        if template_name not in self.templates:
            logger.error(f"Plantilla no encontrada: {template_name}")
            return None
        
        # Cargar screenshot
        screenshot = cv2.imread(screenshot_path, cv2.IMREAD_COLOR)
        if screenshot is None:
            logger.error(f"No se pudo cargar screenshot: {screenshot_path}")
            return None
        
        template = self.templates[template_name]
        
        # Realizar matching
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        if max_val >= threshold:
            # Calcular centro de la plantilla
            h, w = template.shape[:2]
            center_x = max_loc[0] + w // 2
            center_y = max_loc[1] + h // 2
            
            logger.debug(f"Plantilla {template_name} encontrada en ({center_x}, {center_y}) con confianza {max_val:.2f}")
            return (center_x, center_y)
        
        logger.debug(f"Plantilla {template_name} no encontrada (confianza máxima: {max_val:.2f})")
        return None
    
    def detect_raid_available(self, screenshot_path: str) -> bool:
        """Detectar si hay un raid disponible"""
        return self.find_template(screenshot_path, 'raid_button') is not None
    
    def detect_battle_ready(self, screenshot_path: str) -> bool:
        """Detectar si está listo para batallar"""
        return self.find_template(screenshot_path, 'battle_button') is not None
    
    def detect_catch_screen(self, screenshot_path: str) -> bool:
        """Detectar pantalla de captura"""
        pokeball_found = self.find_template(screenshot_path, 'pokeball') is not None
        premier_ball_found = self.find_template(screenshot_path, 'premier_ball') is not None
        return pokeball_found or premier_ball_found
    
    def detect_catch_success(self, screenshot_path: str) -> bool:
        """Detectar captura exitosa"""
        return self.find_template(screenshot_path, 'catch_success') is not None
    
    def detect_loading(self, screenshot_path: str) -> bool:
        """Detectar pantalla de carga"""
        return self.find_template(screenshot_path, 'loading') is not None
    
    def get_pokemon_cp(self, screenshot_path: str) -> Optional[int]:
        """Extraer CP del Pokémon (usando OCR básico)"""
        try:
            # Cargar imagen
            image = cv2.imread(screenshot_path)
            
            # Área aproximada donde aparece el CP (ajustar según resolución)
            height, width = image.shape[:2]
            cp_area = image[int(height*0.1):int(height*0.3), int(width*0.3):int(width*0.7)]
            
            # Convertir a escala de grises
            gray = cv2.cvtColor(cp_area, cv2.COLOR_BGR2GRAY)
            
            # Aplicar threshold para mejorar OCR
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Aquí integrarías con pytesseract para OCR
            # import pytesseract
            # text = pytesseract.image_to_string(thresh, config='--psm 8 -c tessedit_char_whitelist=0123456789CP')
            
            # Por ahora, retornar valor simulado
            return 2500
            
        except Exception as e:
            logger.error(f"Error extrayendo CP: {e}")
            return None
    
    def analyze_screen_state(self, screenshot_path: str) -> Dict[str, bool]:
        """Analizar estado general de la pantalla"""
        state = {
            'raid_available': self.detect_raid_available(screenshot_path),
            'battle_ready': self.detect_battle_ready(screenshot_path),
            'catch_screen': self.detect_catch_screen(screenshot_path),
            'catch_success': self.detect_catch_success(screenshot_path),
            'loading': self.detect_loading(screenshot_path)
        }
        
        logger.debug(f"Estado de pantalla: {state}")
        return state
    
    def create_template_from_screenshot(self, screenshot_path: str, template_name: str, 
                                      x: int, y: int, width: int, height: int):
        """Crear plantilla desde un área de screenshot"""
        screenshot = cv2.imread(screenshot_path)
        if screenshot is None:
            logger.error(f"No se pudo cargar screenshot: {screenshot_path}")
            return False
        
        # Extraer área
        template = screenshot[y:y+height, x:x+width]
        
        # Guardar plantilla
        template_path = self.templates_path / f"{template_name}.png"
        cv2.imwrite(str(template_path), template)
        
        # Cargar en memoria
        self.templates[template_name] = template
        
        logger.info(f"Plantilla creada: {template_name} en {template_path}")
        return True

class ColorDetector:
    """Detector de colores específicos en la pantalla"""
    
    @staticmethod
    def detect_shiny_sparkles(screenshot_path: str) -> bool:
        """Detectar brillos de shiny"""
        try:
            image = cv2.imread(screenshot_path)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Rango de colores para brillos dorados/plateados
            lower_gold = np.array([15, 100, 100])
            upper_gold = np.array([35, 255, 255])
            
            lower_silver = np.array([0, 0, 200])
            upper_silver = np.array([180, 30, 255])
            
            # Crear máscaras
            mask_gold = cv2.inRange(hsv, lower_gold, upper_gold)
            mask_silver = cv2.inRange(hsv, lower_silver, upper_silver)
            
            # Combinar máscaras
            combined_mask = cv2.bitwise_or(mask_gold, mask_silver)
            
            # Contar píxeles brillantes
            bright_pixels = cv2.countNonZero(combined_mask)
            total_pixels = image.shape[0] * image.shape[1]
            
            # Si más del 1% de píxeles son brillantes, probablemente es shiny
            shiny_ratio = bright_pixels / total_pixels
            is_shiny = shiny_ratio > 0.01
            
            logger.debug(f"Ratio de brillo: {shiny_ratio:.4f}, Shiny: {is_shiny}")
            return is_shiny
            
        except Exception as e:
            logger.error(f"Error detectando shiny: {e}")
            return False
    
    @staticmethod
    def detect_health_bar(screenshot_path: str) -> Optional[float]:
        """Detectar nivel de vida del raid boss"""
        try:
            image = cv2.imread(screenshot_path)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Área aproximada de la barra de vida (ajustar según resolución)
            height, width = image.shape[:2]
            health_area = hsv[int(height*0.15):int(height*0.25), int(width*0.1):int(width*0.9)]
            
            # Detectar color verde (vida)
            lower_green = np.array([40, 50, 50])
            upper_green = np.array([80, 255, 255])
            green_mask = cv2.inRange(health_area, lower_green, upper_green)
            
            # Detectar color rojo (vida baja)
            lower_red = np.array([0, 50, 50])
            upper_red = np.array([10, 255, 255])
            red_mask = cv2.inRange(health_area, lower_red, upper_red)
            
            # Calcular porcentaje de vida
            green_pixels = cv2.countNonZero(green_mask)
            red_pixels = cv2.countNonZero(red_mask)
            total_health_pixels = green_pixels + red_pixels
            
            if total_health_pixels > 0:
                health_percentage = green_pixels / total_health_pixels
                logger.debug(f"Vida del boss: {health_percentage:.2%}")
                return health_percentage
            
            return None
            
        except Exception as e:
            logger.error(f"Error detectando vida: {e}")
            return None

def create_sample_templates():
    """Crear plantillas de ejemplo para testing"""
    templates_dir = Path("templates")
    templates_dir.mkdir(exist_ok=True)
    
    # Crear imágenes de ejemplo (rectángulos de colores)
    templates = {
        'raid_button': (100, 40, (0, 255, 0)),  # Verde
        'battle_button': (120, 50, (255, 0, 0)),  # Rojo
        'pokeball': (60, 60, (255, 255, 255)),  # Blanco
        'premier_ball': (60, 60, (255, 215, 0)),  # Dorado
    }
    
    for name, (width, height, color) in templates.items():
        # Crear imagen simple
        img = np.full((height, width, 3), color, dtype=np.uint8)
        
        # Agregar texto
        cv2.putText(img, name[:8], (5, height//2), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 0), 1)
        
        # Guardar
        template_path = templates_dir / f"{name}.png"
        cv2.imwrite(str(template_path), img)
        
        logger.info(f"Plantilla de ejemplo creada: {template_path}")

if __name__ == "__main__":
    # Crear plantillas de ejemplo
    create_sample_templates()
    
    # Probar reconocimiento
    recognizer = PokemonImageRecognition()
    print("Módulo de reconocimiento de imágenes listo")
    print(f"Plantillas cargadas: {list(recognizer.templates.keys())}")
