{"name": "mcp-adb-server", "version": "1.0.0", "description": "MCP Server for Android Debug Bridge (ADB) integration", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "watch": "tsx watch src/index.ts", "test": "jest", "test-adb": "tsx src/test.ts", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "setup": "./setup.sh"}, "keywords": ["mcp", "adb", "android", "debug", "bridge", "model-context-protocol"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "prettier": "^3.1.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}