#!/usr/bin/env node

/**
 * Script de prueba para conexión WiFi con dispositivos Android
 */

import { AdbUtils } from './utils/adb.js';

async function testWifiConnection(): Promise<void> {
  console.log('📶 Probando conexión WiFi con dispositivos Android...\n');

  try {
    // Test 1: Verificar ADB
    console.log('1. Verificando ADB...');
    const isAvailable = await AdbUtils.isAdbAvailable();
    if (!isAvailable) {
      console.log('❌ ADB no está disponible');
      return;
    }
    console.log('✅ ADB disponible\n');

    // Test 2: Listar dispositivos actuales
    console.log('2. Dispositivos conectados actualmente:');
    const devices = await AdbUtils.getDevices();
    if (devices.length === 0) {
      console.log('❌ No hay dispositivos conectados');
      console.log('💡 Conecta un dispositivo por USB primero para configurar WiFi debugging');
      return;
    }

    devices.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device.id} (${device.state})`);
    });
    console.log();

    // Test 3: Obtener dispositivo listo
    console.log('3. Obteniendo dispositivo listo...');
    const device = await AdbUtils.getDevice();
    console.log(`✅ Dispositivo: ${device.id}\n`);

    // Test 4: Obtener IP del dispositivo
    console.log('4. Obteniendo IP del dispositivo...');
    try {
      const ipAddress = await AdbUtils.getDeviceIpAddress(device.id);
      console.log(`✅ IP del dispositivo: ${ipAddress}\n`);

      // Test 5: Habilitar WiFi debugging
      console.log('5. Habilitando WiFi debugging...');
      const enableResult = await AdbUtils.enableWifiDebugging(device.id);
      console.log(`✅ ${enableResult}\n`);

      // Test 6: Esperar y conectar por WiFi
      console.log('6. Esperando 10 segundos para que el dispositivo reinicie ADB...');
      await new Promise(resolve => setTimeout(resolve, 10000));

      console.log('7. Intentando conectar por WiFi...');
      try {
        const connectResult = await AdbUtils.connectWifi(ipAddress);
        console.log(`✅ ${connectResult}\n`);

        // Test 7: Verificar dispositivos después de conexión WiFi
        console.log('8. Verificando dispositivos después de conexión WiFi:');
        const devicesAfterWifi = await AdbUtils.getDevices();
        devicesAfterWifi.forEach((dev, index) => {
          const connectionType = dev.id.includes(':') ? '📶 WiFi' : '🔌 USB';
          console.log(`   ${index + 1}. ${dev.id} (${dev.state}) ${connectionType}`);
        });
        console.log();

        // Test 8: Probar comando en dispositivo WiFi
        console.log('9. Probando comando en dispositivo WiFi...');
        const wifiDevice = devicesAfterWifi.find(d => d.id.includes(':'));
        if (wifiDevice) {
          const androidVersion = await AdbUtils.executeShellCommand(
            'getprop ro.build.version.release', 
            wifiDevice.id
          );
          console.log(`✅ Comando ejecutado en WiFi - Android: ${androidVersion}\n`);

          // Test 9: Información del sistema por WiFi
          console.log('10. Obteniendo información del sistema por WiFi...');
          const manufacturer = await AdbUtils.executeShellCommand(
            'getprop ro.product.manufacturer', 
            wifiDevice.id
          );
          const model = await AdbUtils.executeShellCommand(
            'getprop ro.product.model', 
            wifiDevice.id
          );
          console.log(`✅ Fabricante: ${manufacturer}`);
          console.log(`✅ Modelo: ${model}\n`);

          console.log('🎉 ¡Conexión WiFi exitosa! Ahora puedes:');
          console.log('   - Desconectar el cable USB');
          console.log('   - Usar el dispositivo de forma inalámbrica');
          console.log(`   - El dispositivo aparece como: ${wifiDevice.id}`);

        } else {
          console.log('⚠️  No se encontró dispositivo WiFi en la lista');
        }

      } catch (wifiError) {
        console.log(`❌ Error conectando por WiFi: ${wifiError}`);
        console.log('\n🔧 Posibles soluciones:');
        console.log('   - Verifica que el dispositivo y la PC estén en la misma red WiFi');
        console.log('   - Verifica que el firewall no bloquee el puerto 5555');
        console.log('   - Intenta manualmente: adb connect ' + ipAddress + ':5555');
      }

    } catch (ipError) {
      console.log(`❌ Error obteniendo IP: ${ipError}`);
      console.log('\n🔧 Alternativas:');
      console.log('   - Encuentra la IP manualmente en Configuración > WiFi');
      console.log('   - Usa: adb_enable_wifi_debugging para habilitar WiFi debugging');
      console.log('   - Luego usa: adb_connect_wifi con la IP manual');
    }

  } catch (error) {
    console.error('❌ Error durante las pruebas WiFi:', error);
  }
}

async function testWifiCommands(): Promise<void> {
  console.log('\n📋 Probando comandos específicos de WiFi...\n');

  try {
    // Listar dispositivos WiFi
    console.log('1. Dispositivos WiFi conectados:');
    const devices = await AdbUtils.getDevices();
    const wifiDevices = devices.filter(d => d.id.includes(':'));
    
    if (wifiDevices.length === 0) {
      console.log('❌ No hay dispositivos WiFi conectados');
      return;
    }

    wifiDevices.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device.id} (${device.state})`);
    });

    const wifiDevice = wifiDevices[0];
    if (!wifiDevice) {
      console.log('❌ No se encontró dispositivo WiFi válido');
      return;
    }
    console.log(`\n📱 Usando dispositivo: ${wifiDevice.id}\n`);

    // Test comandos básicos
    const commands = [
      { name: 'Versión Android', cmd: 'getprop ro.build.version.release' },
      { name: 'Nivel API', cmd: 'getprop ro.build.version.sdk' },
      { name: 'Fabricante', cmd: 'getprop ro.product.manufacturer' },
      { name: 'Modelo', cmd: 'getprop ro.product.model' },
      { name: 'Batería', cmd: 'dumpsys battery | grep level' },
      { name: 'Memoria libre', cmd: 'cat /proc/meminfo | grep MemAvailable' },
      { name: 'Espacio en /data', cmd: 'df -h /data | tail -1' },
    ];

    for (const { name, cmd } of commands) {
      try {
        console.log(`🔍 ${name}:`);
        const result = await AdbUtils.executeShellCommand(cmd, wifiDevice.id);
        console.log(`   ✅ ${result.trim()}`);
      } catch (error) {
        console.log(`   ❌ Error: ${error}`);
      }
    }

    console.log('\n🎯 Comandos WiFi completados exitosamente!');

  } catch (error) {
    console.error('❌ Error probando comandos WiFi:', error);
  }
}

async function main(): Promise<void> {
  console.log('📶 MCP ADB Server - Pruebas de Conexión WiFi');
  console.log('============================================\n');

  // Verificar si hay argumentos de línea de comandos
  const args = process.argv.slice(2);
  
  if (args.includes('--commands-only')) {
    await testWifiCommands();
  } else {
    await testWifiConnection();
    
    // Preguntar si quiere probar comandos
    console.log('\n❓ ¿Quieres probar comandos en dispositivos WiFi conectados?');
    console.log('   Ejecuta: npm run test-wifi -- --commands-only');
  }

  console.log('\n📝 Notas importantes:');
  console.log('- El dispositivo y la PC deben estar en la misma red WiFi');
  console.log('- Algunos routers pueden bloquear conexiones entre dispositivos');
  console.log('- El puerto 5555 debe estar abierto en el firewall');
  console.log('- Una vez conectado por WiFi, puedes desconectar el USB');
  
  console.log('\n🔧 Comandos útiles:');
  console.log('- adb_setup_wifi_debugging: Configuración automática');
  console.log('- adb_connect_wifi: Conectar a IP específica');
  console.log('- adb_disconnect_wifi: Desconectar WiFi');
  console.log('- adb_get_device_ip: Obtener IP del dispositivo');
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Error ejecutando pruebas WiFi:', error);
    process.exit(1);
  });
}
