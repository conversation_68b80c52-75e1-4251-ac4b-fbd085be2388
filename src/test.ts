#!/usr/bin/env node

/**
 * Script de prueba básico para verificar funcionalidad del servidor MCP ADB
 */

import { AdbUtils } from './utils/adb.js';

async function testAdbConnection(): Promise<void> {
  console.log('🔍 Probando conexión ADB...\n');

  try {
    // Test 1: Verificar si ADB está disponible
    console.log('1. Verificando disponibilidad de ADB...');
    const isAvailable = await AdbUtils.isAdbAvailable();
    console.log(`   ✅ ADB disponible: ${isAvailable}\n`);

    if (!isAvailable) {
      console.log('❌ ADB no está disponible. Instala Android SDK y agrega ADB al PATH.');
      return;
    }

    // Test 2: Listar dispositivos
    console.log('2. Listando dispositivos conectados...');
    const devices = await AdbUtils.getDevices();
    console.log(`   📱 Dispositivos encontrados: ${devices.length}`);
    
    if (devices.length === 0) {
      console.log('   ⚠️  No hay dispositivos conectados');
      console.log('   💡 Conecta un dispositivo Android con USB debugging habilitado');
      return;
    }

    devices.forEach((device, index) => {
      console.log(`   ${index + 1}. ID: ${device.id}, Estado: ${device.state}`);
      if (device.model) console.log(`      Modelo: ${device.model}`);
      if (device.product) console.log(`      Producto: ${device.product}`);
    });
    console.log();

    // Test 3: Obtener dispositivo listo
    console.log('3. Obteniendo dispositivo listo...');
    try {
      const device = await AdbUtils.getDevice();
      console.log(`   ✅ Dispositivo listo: ${device.id} (${device.state})\n`);

      // Test 4: Ejecutar comando simple
      console.log('4. Ejecutando comando de prueba...');
      const androidVersion = await AdbUtils.executeShellCommand('getprop ro.build.version.release');
      console.log(`   📋 Versión de Android: ${androidVersion}\n`);

      // Test 5: Obtener información básica del sistema
      console.log('5. Obteniendo información del sistema...');
      const manufacturer = await AdbUtils.executeShellCommand('getprop ro.product.manufacturer');
      const model = await AdbUtils.executeShellCommand('getprop ro.product.model');
      const apiLevel = await AdbUtils.executeShellCommand('getprop ro.build.version.sdk');
      
      console.log(`   🏭 Fabricante: ${manufacturer}`);
      console.log(`   📱 Modelo: ${model}`);
      console.log(`   🔢 API Level: ${apiLevel}\n`);

      // Test 6: Listar algunos paquetes
      console.log('6. Listando paquetes de ejemplo...');
      const packages = await AdbUtils.getPackages();
      console.log(`   📦 Total de paquetes: ${packages.length}`);
      console.log(`   📋 Primeros 5 paquetes:`);
      packages.slice(0, 5).forEach((pkg, index) => {
        console.log(`      ${index + 1}. ${pkg.name}`);
      });
      console.log();

      console.log('✅ ¡Todas las pruebas básicas completadas exitosamente!');
      console.log('🚀 El servidor MCP ADB está listo para usar.');

    } catch (error) {
      console.log(`   ❌ Error obteniendo dispositivo listo: ${error}`);
      console.log('   💡 Verifica que el dispositivo esté autorizado y en modo "device"');
    }

  } catch (error) {
    console.error('❌ Error durante las pruebas:', error);
  }
}

async function main(): Promise<void> {
  console.log('🤖 MCP ADB Server - Pruebas de Funcionalidad');
  console.log('===========================================\n');

  await testAdbConnection();

  console.log('\n📝 Notas:');
  console.log('- Si hay errores, verifica que ADB esté instalado y en el PATH');
  console.log('- Asegúrate de que el dispositivo tenga USB debugging habilitado');
  console.log('- Acepta el prompt de autorización en el dispositivo si aparece');
  console.log('\n🔧 Para usar el servidor MCP:');
  console.log('1. Ejecuta: npm run dev');
  console.log('2. Configura Claude Desktop con claude_desktop_config.json');
  console.log('3. Reinicia Claude Desktop');
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Error ejecutando pruebas:', error);
    process.exit(1);
  });
}
