import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import type { Device, Package } from '../types/adb.js';

const execAsync = promisify(exec);

export class AdbError extends Error {
  constructor(message: string, public code?: number) {
    super(message);
    this.name = 'AdbError';
  }
}

export class AdbUtils {
  private static adbPath = 'adb';

  /**
   * Set custom ADB path if needed
   */
  static setAdbPath(path: string): void {
    this.adbPath = path;
  }

  /**
   * Execute ADB command and return output
   */
  static async executeCommand(
    args: string[],
    timeout = 30000
  ): Promise<string> {
    // Validate arguments
    if (!args || args.length === 0) {
      throw new AdbError('No ADB command arguments provided');
    }

    // Sanitize arguments to prevent command injection
    const sanitizedArgs = args.map(arg => {
      if (typeof arg !== 'string') {
        throw new AdbError('All ADB command arguments must be strings');
      }
      // Basic sanitization - remove dangerous characters
      return arg.replace(/[;&|`$(){}[\]]/g, '');
    });

    const command = `${this.adbPath} ${sanitizedArgs.join(' ')}`;

    try {
      const { stdout, stderr } = await execAsync(command, {
        timeout,
        maxBuffer: 1024 * 1024 * 10, // 10MB buffer
      });

      // Check for common ADB errors in stderr
      if (stderr) {
        const lowerStderr = stderr.toLowerCase();
        if (lowerStderr.includes('device not found') ||
            lowerStderr.includes('no devices/emulators found')) {
          throw new AdbError('No devices connected or device not found');
        }
        if (lowerStderr.includes('device offline')) {
          throw new AdbError('Device is offline');
        }
        if (lowerStderr.includes('unauthorized')) {
          throw new AdbError('Device is unauthorized - please accept USB debugging prompt');
        }
        if (lowerStderr.includes('permission denied')) {
          throw new AdbError('Permission denied - check device permissions');
        }
        // Allow daemon messages and other non-critical stderr
        if (!lowerStderr.includes('* daemon') &&
            !lowerStderr.includes('adb server') &&
            stderr.trim().length > 0) {
          console.warn(`ADB warning: ${stderr}`);
        }
      }

      return stdout.trim();
    } catch (error) {
      if (error instanceof AdbError) {
        throw error;
      }
      if (error instanceof Error) {
        if (error.message.includes('ENOENT')) {
          throw new AdbError('ADB not found - please install Android SDK and add ADB to PATH');
        }
        if (error.message.includes('timeout')) {
          throw new AdbError(`ADB command timed out after ${timeout}ms`);
        }
        throw new AdbError(`Failed to execute ADB command: ${error.message}`);
      }
      throw new AdbError('Unknown error executing ADB command');
    }
  }

  /**
   * Check if ADB is available
   */
  static async isAdbAvailable(): Promise<boolean> {
    try {
      await this.executeCommand(['version']);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get list of connected devices
   */
  static async getDevices(): Promise<Device[]> {
    const output = await this.executeCommand(['devices', '-l']);
    const lines = output.split('\n').slice(1); // Skip header
    
    const devices: Device[] = [];
    
    for (const line of lines) {
      if (!line.trim()) continue;
      
      const parts = line.trim().split(/\s+/);
      if (parts.length < 2) continue;
      
      const [id, state] = parts;
      const device: Device = { id, state: state as Device['state'] };
      
      // Parse additional properties
      for (let i = 2; i < parts.length; i++) {
        const part = parts[i];
        if (part?.includes(':')) {
          const [key, value] = part.split(':', 2);
          if (key && value) {
            switch (key) {
              case 'product':
                device.product = value;
                break;
              case 'model':
                device.model = value;
                break;
              case 'device':
                device.device = value;
                break;
              case 'transport_id':
                device.transport_id = value;
                break;
            }
          }
        }
      }
      
      devices.push(device);
    }
    
    return devices;
  }

  /**
   * Get the first available device or specific device
   */
  static async getDevice(deviceId?: string): Promise<Device> {
    const devices = await this.getDevices();

    if (devices.length === 0) {
      throw new AdbError('No devices connected. Please connect an Android device and enable USB debugging.');
    }

    if (deviceId) {
      // Validate device ID format
      if (typeof deviceId !== 'string' || deviceId.trim().length === 0) {
        throw new AdbError('Invalid device ID provided');
      }

      const device = devices.find(d => d.id === deviceId);
      if (!device) {
        const availableIds = devices.map(d => d.id).join(', ');
        throw new AdbError(`Device ${deviceId} not found. Available devices: ${availableIds}`);
      }

      if (device.state !== 'device') {
        let stateMessage = `Device ${deviceId} is not ready (state: ${device.state})`;
        switch (device.state) {
          case 'unauthorized':
            stateMessage += '. Please accept USB debugging prompt on device.';
            break;
          case 'offline':
            stateMessage += '. Please check USB connection and try reconnecting device.';
            break;
          case 'bootloader':
            stateMessage += '. Device is in bootloader mode.';
            break;
          case 'recovery':
            stateMessage += '. Device is in recovery mode.';
            break;
        }
        throw new AdbError(stateMessage);
      }
      return device;
    }

    const readyDevice = devices.find(d => d.state === 'device');
    if (!readyDevice) {
      const deviceStates = devices.map(d => `${d.id}: ${d.state}`).join(', ');
      throw new AdbError(`No ready devices found. Device states: ${deviceStates}`);
    }

    return readyDevice;
  }

  /**
   * Execute shell command on device
   */
  static async executeShellCommand(
    command: string,
    deviceId?: string,
    timeout = 30000
  ): Promise<string> {
    const device = await this.getDevice(deviceId);
    const args = ['-s', device.id, 'shell', command];
    return this.executeCommand(args, timeout);
  }

  /**
   * Get installed packages on device
   */
  static async getPackages(deviceId?: string): Promise<Package[]> {
    const output = await this.executeShellCommand('pm list packages -f', deviceId);
    const lines = output.split('\n');
    
    const packages: Package[] = [];
    
    for (const line of lines) {
      if (!line.startsWith('package:')) continue;
      
      const match = line.match(/package:(.+)=(.+)/);
      if (match) {
        const [, , packageName] = match;
        if (packageName) {
          packages.push({ name: packageName });
        }
      }
    }
    
    return packages;
  }

  /**
   * Install APK on device
   */
  static async installApk(
    apkPath: string,
    deviceId?: string,
    options: {
      replace?: boolean;
      allowDowngrade?: boolean;
      grantPermissions?: boolean;
    } = {}
  ): Promise<string> {
    const device = await this.getDevice(deviceId);
    const args = ['-s', device.id, 'install'];
    
    if (options.replace) args.push('-r');
    if (options.allowDowngrade) args.push('-d');
    if (options.grantPermissions) args.push('-g');
    
    args.push(apkPath);
    
    return this.executeCommand(args, 60000); // Longer timeout for installation
  }

  /**
   * Uninstall package from device
   */
  static async uninstallPackage(
    packageName: string,
    deviceId?: string
  ): Promise<string> {
    const device = await this.getDevice(deviceId);
    const args = ['-s', device.id, 'uninstall', packageName];
    return this.executeCommand(args);
  }

  /**
   * Push file to device
   */
  static async pushFile(
    localPath: string,
    remotePath: string,
    deviceId?: string
  ): Promise<string> {
    // Validate input parameters
    if (!localPath || typeof localPath !== 'string') {
      throw new AdbError('Local path is required and must be a string');
    }
    if (!remotePath || typeof remotePath !== 'string') {
      throw new AdbError('Remote path is required and must be a string');
    }

    // Basic path validation
    if (localPath.includes('..') || remotePath.includes('..')) {
      throw new AdbError('Path traversal not allowed');
    }

    const device = await this.getDevice(deviceId);
    const args = ['-s', device.id, 'push', localPath, remotePath];

    try {
      return await this.executeCommand(args, 60000);
    } catch (error) {
      if (error instanceof AdbError) {
        if (error.message.includes('No such file')) {
          throw new AdbError(`Local file not found: ${localPath}`);
        }
        if (error.message.includes('Permission denied')) {
          throw new AdbError(`Permission denied. Cannot write to: ${remotePath}`);
        }
      }
      throw error;
    }
  }

  /**
   * Pull file from device
   */
  static async pullFile(
    remotePath: string,
    localPath: string,
    deviceId?: string
  ): Promise<string> {
    // Validate input parameters
    if (!remotePath || typeof remotePath !== 'string') {
      throw new AdbError('Remote path is required and must be a string');
    }
    if (!localPath || typeof localPath !== 'string') {
      throw new AdbError('Local path is required and must be a string');
    }

    // Basic path validation
    if (localPath.includes('..') || remotePath.includes('..')) {
      throw new AdbError('Path traversal not allowed');
    }

    const device = await this.getDevice(deviceId);
    const args = ['-s', device.id, 'pull', remotePath, localPath];

    try {
      return await this.executeCommand(args, 60000);
    } catch (error) {
      if (error instanceof AdbError) {
        if (error.message.includes('No such file')) {
          throw new AdbError(`Remote file not found: ${remotePath}`);
        }
        if (error.message.includes('Permission denied')) {
          throw new AdbError(`Permission denied. Cannot access: ${remotePath}`);
        }
      }
      throw error;
    }
  }

  /**
   * Take screenshot
   */
  static async takeScreenshot(
    outputPath: string,
    deviceId?: string
  ): Promise<string> {
    const device = await this.getDevice(deviceId);
    
    // First take screenshot on device
    await this.executeShellCommand('screencap -p /sdcard/screenshot.png', device.id);
    
    // Then pull it to local machine
    await this.pullFile('/sdcard/screenshot.png', outputPath, device.id);
    
    // Clean up
    await this.executeShellCommand('rm /sdcard/screenshot.png', device.id);
    
    return `Screenshot saved to ${outputPath}`;
  }

  /**
   * Get logcat output
   */
  static async getLogcat(
    options: {
      deviceId?: string;
      tag?: string;
      priority?: string;
      lines?: number;
      clear?: boolean;
    } = {}
  ): Promise<string> {
    const device = await this.getDevice(options.deviceId);
    const args = ['-s', device.id, 'logcat'];
    
    if (options.clear) {
      args.push('-c');
      await this.executeCommand(args);
      return 'Logcat cleared';
    }
    
    args.push('-d'); // Dump and exit
    
    if (options.lines) {
      args.push('-t', options.lines.toString());
    }
    
    if (options.tag) {
      args.push('-s', options.tag);
    }
    
    if (options.priority) {
      args.push(`*:${options.priority}`);
    }
    
    return this.executeCommand(args);
  }
}
