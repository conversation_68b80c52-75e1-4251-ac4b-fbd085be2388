#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

// Import all tool handlers
import {
  listDevicesTool,
  handleListDevices,
  getDeviceInfoTool,
  handleGetDeviceInfo,
  checkAdbStatusTool,
  handleCheckAdbStatus,
} from './tools/device-tools.js';

import {
  executeShellCommandTool,
  handleExecuteShellCommand,
  getSystemInfoTool,
  handleGetSystemInfo,
  getProcessesTool,
  handleGetProcesses,
  getNetworkInfoTool,
  handleGetNetworkInfo,
} from './tools/shell-tools.js';

import {
  listPackagesTool,
  handleListPackages,
  getPackageInfoTool,
  handleGetPackageInfo,
  installApkTool,
  handleInstallApk,
  uninstallPackageTool,
  handleUninstallPackage,
  controlAppTool,
  handleControlApp,
} from './tools/app-tools.js';

import {
  pushFileTool,
  handlePushFile,
  pullFileTool,
  handlePullFile,
  listFilesTool,
  handleListFiles,
  takeScreenshotTool,
  handleTakeScreenshot,
  getLogcatTool,
  handleGetLogcat,
  getStorageInfoTool,
  handleGetStorageInfo,
} from './tools/file-tools.js';

// Define all available tools
const TOOLS = [
  // Device management tools
  listDevicesTool,
  getDeviceInfoTool,
  checkAdbStatusTool,
  
  // Shell and system tools
  executeShellCommandTool,
  getSystemInfoTool,
  getProcessesTool,
  getNetworkInfoTool,
  
  // Application management tools
  listPackagesTool,
  getPackageInfoTool,
  installApkTool,
  uninstallPackageTool,
  controlAppTool,
  
  // File and media tools
  pushFileTool,
  pullFileTool,
  listFilesTool,
  takeScreenshotTool,
  getLogcatTool,
  getStorageInfoTool,
];

// Tool handlers mapping
const TOOL_HANDLERS = {
  // Device management handlers
  adb_list_devices: handleListDevices,
  adb_get_device_info: handleGetDeviceInfo,
  adb_check_status: handleCheckAdbStatus,
  
  // Shell and system handlers
  adb_shell: handleExecuteShellCommand,
  adb_get_system_info: handleGetSystemInfo,
  adb_get_processes: handleGetProcesses,
  adb_get_network_info: handleGetNetworkInfo,
  
  // Application management handlers
  adb_list_packages: handleListPackages,
  adb_get_package_info: handleGetPackageInfo,
  adb_install_apk: handleInstallApk,
  adb_uninstall_package: handleUninstallPackage,
  adb_control_app: handleControlApp,
  
  // File and media handlers
  adb_push_file: handlePushFile,
  adb_pull_file: handlePullFile,
  adb_list_files: handleListFiles,
  adb_screenshot: handleTakeScreenshot,
  adb_logcat: handleGetLogcat,
  adb_get_storage_info: handleGetStorageInfo,
};

class AdbMcpServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: 'adb-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  private setupToolHandlers(): void {
    // Handle tool listing
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: TOOLS,
      };
    });

    // Handle tool execution
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      const handler = TOOL_HANDLERS[name as keyof typeof TOOL_HANDLERS];
      if (!handler) {
        throw new Error(`Unknown tool: ${name}`);
      }

      try {
        const result = await handler(args);
        return {
          content: [
            {
              type: 'text',
              text: result,
            },
          ],
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
          content: [
            {
              type: 'text',
              text: `Error executing tool ${name}: ${errorMessage}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private setupErrorHandling(): void {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    
    // Log server start (to stderr so it doesn't interfere with MCP protocol)
    console.error('ADB MCP Server started successfully');
    console.error(`Available tools: ${TOOLS.length}`);
    console.error('Waiting for requests...');
  }
}

// Start the server
async function main(): Promise<void> {
  const server = new AdbMcpServer();
  await server.run();
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}
