#!/usr/bin/env node

/**
 * Cliente de prueba MCP para simular c<PERSON><PERSON> Claude Desktop interactuaría con nuestro servidor
 */

import { spawn } from 'child_process';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';

class McpTestClient {
  private client: Client;
  private transport: StdioClientTransport;

  constructor() {
    // Crear el proceso del servidor MCP
    const serverProcess = spawn('node', ['dist/index.js'], {
      cwd: process.cwd(),
      stdio: ['pipe', 'pipe', 'inherit'],
    });

    // Crear el transporte
    this.transport = new StdioClientTransport({
      stdin: serverProcess.stdin!,
      stdout: serverProcess.stdout!,
    });

    // Crear el cliente
    this.client = new Client(
      {
        name: 'test-client',
        version: '1.0.0',
      },
      {
        capabilities: {},
      }
    );
  }

  async connect(): Promise<void> {
    await this.client.connect(this.transport);
    console.log('🔗 Conectado al servidor MCP ADB');
  }

  async listTools(): Promise<void> {
    console.log('\n📋 Listando herramientas disponibles...');
    
    try {
      const response = await this.client.request(
        { method: 'tools/list' },
        { method: 'tools/list' }
      );

      console.log(`\n✅ Herramientas encontradas: ${response.tools?.length || 0}\n`);

      if (response.tools) {
        response.tools.forEach((tool, index) => {
          console.log(`${index + 1}. 🛠️  ${tool.name}`);
          console.log(`   📝 ${tool.description}`);
          console.log('');
        });
      }
    } catch (error) {
      console.error('❌ Error listando herramientas:', error);
    }
  }

  async callTool(name: string, args: any = {}): Promise<void> {
    console.log(`\n🔧 Ejecutando herramienta: ${name}`);
    console.log(`📋 Argumentos:`, JSON.stringify(args, null, 2));

    try {
      const response = await this.client.request(
        {
          method: 'tools/call',
          params: {
            name,
            arguments: args,
          },
        },
        {
          method: 'tools/call',
          params: {
            name,
            arguments: args,
          },
        }
      );

      console.log('\n✅ Resultado:');
      if (response.content) {
        response.content.forEach((content) => {
          if (content.type === 'text') {
            console.log(content.text);
          }
        });
      }
    } catch (error) {
      console.error('❌ Error ejecutando herramienta:', error);
    }
  }

  async runDemo(): Promise<void> {
    console.log('🎯 Ejecutando demo de herramientas MCP ADB...\n');

    // Demo 1: Verificar estado de ADB
    await this.callTool('adb_check_status');

    // Demo 2: Listar dispositivos
    await this.callTool('adb_list_devices');

    // Demo 3: Obtener información del sistema (si hay dispositivos)
    console.log('\n⏳ Intentando obtener información del sistema...');
    await this.callTool('adb_get_system_info');

    // Demo 4: Listar archivos (si hay dispositivos)
    console.log('\n⏳ Intentando listar archivos...');
    await this.callTool('adb_list_files', { path: '/sdcard' });

    // Demo 5: Obtener IP del dispositivo (si hay dispositivos)
    console.log('\n⏳ Intentando obtener IP del dispositivo...');
    await this.callTool('adb_get_device_ip');

    console.log('\n🎉 Demo completado!');
  }

  async close(): Promise<void> {
    await this.client.close();
    console.log('\n🔌 Desconectado del servidor MCP');
  }
}

async function main(): Promise<void> {
  console.log('🤖 Cliente de Prueba MCP ADB');
  console.log('============================\n');

  const client = new McpTestClient();

  try {
    await client.connect();
    await client.listTools();
    await client.runDemo();
  } catch (error) {
    console.error('❌ Error en el cliente MCP:', error);
  } finally {
    await client.close();
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Error ejecutando cliente de prueba:', error);
    process.exit(1);
  });
}
