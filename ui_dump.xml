<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.nianticlabs.pokemongo" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,68][695,1353]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.nianticlabs.pokemongo" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,68][695,1353]"><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.nianticlabs.pokemongo" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,68][695,1353]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.nianticlabs.pokemongo" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,68][695,1353]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.nianticlabs.pokemongo" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,68][695,1353]"><node index="0" text="" resource-id="com.nianticlabs.pokemongo:id/unitySurfaceView" class="android.view.View" package="com.nianticlabs.pokemongo" content-desc="Game view" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="true" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,68][695,1353]" /></node></node></node></node><node index="1" text="" resource-id="android:id/statusBarBackground" class="android.view.View" package="com.nianticlabs.pokemongo" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,68][695,69]" /><node index="2" text="" resource-id="android:id/navigationBarBackground" class="android.view.View" package="com.nianticlabs.pokemongo" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][0,0]" /></node></hierarchy>