#!/usr/bin/env python3
"""
Configuración de Playwright para capturar acciones en PGTools
"""

import subprocess
import sys
import os

def install_playwright():
    """Instalar Playwright y dependencias"""
    print("🎭 Instalando Playwright...")
    
    # Instalar playwright
    subprocess.run([sys.executable, "-m", "pip", "install", "playwright"], check=True)
    
    # Instalar navegadores
    subprocess.run([sys.executable, "-m", "playwright", "install"], check=True)
    
    print("✅ Playwright instalado correctamente")

def start_codegen():
    """Iniciar el generador de código de Playwright"""
    print("🎬 Iniciando grabación de Playwright...")
    print("📝 Se abrirá un navegador donde puedes interactuar con la página")
    print("🔴 Todas tus acciones serán grabadas automáticamente")
    print("⏹️  Cierra el navegador cuando termines para generar el código")
    
    # Comando para grabar acciones en pgtools.net/raids
    cmd = [
        sys.executable, "-m", "playwright", "codegen",
        "--target", "python",
        "--output", "pgtools_automation.py",
        "https://pgtools.net/raids"
    ]
    
    subprocess.run(cmd)
    
    print("✅ Grabación completada!")
    print("📄 Código generado en: pgtools_automation.py")

if __name__ == "__main__":
    try:
        print("🚀 Configurando Playwright para PGTools...")
        
        # Verificar si playwright está instalado
        try:
            import playwright
            print("✅ Playwright ya está instalado")
        except ImportError:
            install_playwright()
        
        # Iniciar grabación
        start_codegen()
        
    except KeyboardInterrupt:
        print("\n⏹️  Grabación cancelada")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
