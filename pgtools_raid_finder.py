#!/usr/bin/env python3
"""
Automatización para PGTools - Buscador de Raids
Basado en las acciones capturadas con Playwright
"""

import argparse
import json
import sys
import time
from typing import List, Dict, Optional, Any
from playwright.sync_api import Playwright, sync_playwright, expect
from dataclasses import dataclass
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class RaidResult:
    """Estructura para almacenar resultados de raids"""
    pokemon_name: str
    pokemon_id: int
    raid_level: int
    gym_name: str
    latitude: float
    longitude: float
    distance_km: float
    time_left: str
    cp: Optional[int] = None

class PGToolsRaidFinder:
    """Automatización para buscar raids en PGTools"""
    
    def __init__(self, headless: bool = False, timeout: int = 30000):
        self.headless = headless
        self.timeout = timeout
        self.results: List[RaidResult] = []
    
    def search_raids(self, latitude: float, longitude: float, 
                    pokemon_id: Optional[int] = None, 
                    raid_level: Optional[int] = None,
                    max_distance: float = 20.0) -> List[RaidResult]:
        """Buscar raids en PGTools con parámetros específicos"""
        
        logger.info(f"Buscando raids en coordenadas: {latitude}, {longitude}")
        if pokemon_id:
            logger.info(f"Pokémon específico: ID {pokemon_id}")
        if raid_level:
            logger.info(f"Nivel de raid: {raid_level}")
        
        with sync_playwright() as playwright:
            return self._run_search(playwright, latitude, longitude, pokemon_id, raid_level, max_distance)
    
    def _run_search(self, playwright: Playwright, lat: float, lon: float, 
                   pokemon_id: Optional[int], raid_level: Optional[int], 
                   max_distance: float) -> List[RaidResult]:
        """Ejecutar la búsqueda usando Playwright"""
        
        # Configurar navegador
        browser = playwright.chromium.launch(headless=self.headless)
        context = browser.new_context()
        page = context.new_page()
        page.set_default_timeout(self.timeout)
        
        try:
            # Ir a PGTools raids
            logger.info("Navegando a PGTools...")
            page.goto("https://pgtools.net/raids")
            
            # Esperar a que cargue la página
            page.wait_for_load_state("networkidle")
            
            # Abrir configuración y filtros
            logger.info("Abriendo configuración...")
            page.get_by_role("button", name="right Configs and Filters").click()
            
            # Introducir coordenadas
            coordinates = f"{lat},{lon}"
            logger.info(f"Introduciendo coordenadas: {coordinates}")
            
            coord_input = page.get_by_role("textbox", name="Enter your coordinates")
            coord_input.click()
            coord_input.clear()
            coord_input.fill(coordinates)
            coord_input.press("Enter")
            
            # Esperar un poco para que procese
            time.sleep(2)
            
            # Aplicar filtros adicionales si se especifican
            if pokemon_id:
                self._set_pokemon_filter(page, pokemon_id)
            
            if raid_level:
                self._set_raid_level_filter(page, raid_level)
            
            if max_distance != 20.0:
                self._set_distance_filter(page, max_distance)
            
            # Buscar raids
            logger.info("Buscando raids...")
            self._trigger_search(page)
            
            # Esperar resultados
            time.sleep(3)
            
            # Extraer resultados
            results = self._extract_results(page)
            
            logger.info(f"Encontrados {len(results)} raids")
            return results
            
        except Exception as e:
            logger.error(f"Error durante la búsqueda: {e}")
            return []
        
        finally:
            context.close()
            browser.close()
    
    def _set_pokemon_filter(self, page, pokemon_id: int):
        """Configurar filtro de Pokémon específico"""
        try:
            logger.info(f"Configurando filtro para Pokémon ID: {pokemon_id}")
            
            # Buscar campo de Pokémon ID (ajustar selector según la interfaz real)
            pokemon_input = page.locator("input[placeholder*='Pokemon']").first
            if pokemon_input.is_visible():
                pokemon_input.click()
                pokemon_input.fill(str(pokemon_id))
                pokemon_input.press("Enter")
                
        except Exception as e:
            logger.warning(f"No se pudo configurar filtro de Pokémon: {e}")
    
    def _set_raid_level_filter(self, page, raid_level: int):
        """Configurar filtro de nivel de raid"""
        try:
            logger.info(f"Configurando filtro para nivel: {raid_level}")
            
            # Buscar selector de nivel (ajustar según interfaz real)
            level_selector = page.locator(f"button[data-level='{raid_level}']").first
            if level_selector.is_visible():
                level_selector.click()
                
        except Exception as e:
            logger.warning(f"No se pudo configurar filtro de nivel: {e}")
    
    def _set_distance_filter(self, page, max_distance: float):
        """Configurar filtro de distancia máxima"""
        try:
            logger.info(f"Configurando distancia máxima: {max_distance}km")
            
            # Buscar campo de distancia
            distance_input = page.locator("input[placeholder*='distance']").first
            if distance_input.is_visible():
                distance_input.click()
                distance_input.fill(str(max_distance))
                distance_input.press("Enter")
                
        except Exception as e:
            logger.warning(f"No se pudo configurar filtro de distancia: {e}")
    
    def _trigger_search(self, page):
        """Activar la búsqueda de raids"""
        try:
            # Buscar botón de búsqueda
            search_button = page.locator("button:has-text('Search')").first
            if search_button.is_visible():
                search_button.click()
            else:
                # Alternativamente, presionar Enter en el campo de coordenadas
                page.get_by_role("textbox", name="Enter your coordinates").press("Enter")
                
        except Exception as e:
            logger.warning(f"No se pudo activar búsqueda: {e}")
    
    def _extract_results(self, page) -> List[RaidResult]:
        """Extraer resultados de raids de la página"""
        results = []
        
        try:
            # Esperar a que aparezcan los resultados
            page.wait_for_selector(".raid-item, .raid-card, [data-raid]", timeout=10000)
            
            # Extraer elementos de raid (ajustar selectores según la interfaz real)
            raid_elements = page.locator(".raid-item, .raid-card, [data-raid]").all()
            
            for element in raid_elements:
                try:
                    # Extraer información de cada raid
                    pokemon_name = self._extract_text(element, ".pokemon-name, .name")
                    gym_name = self._extract_text(element, ".gym-name, .location")
                    distance = self._extract_text(element, ".distance")
                    time_left = self._extract_text(element, ".time, .timer")
                    
                    # Extraer coordenadas si están disponibles
                    lat_text = self._extract_text(element, "[data-lat]")
                    lon_text = self._extract_text(element, "[data-lon]")
                    
                    # Crear resultado
                    result = RaidResult(
                        pokemon_name=pokemon_name or "Unknown",
                        pokemon_id=0,  # Se puede mapear después
                        raid_level=0,  # Se puede extraer del elemento
                        gym_name=gym_name or "Unknown Gym",
                        latitude=float(lat_text) if lat_text else 0.0,
                        longitude=float(lon_text) if lon_text else 0.0,
                        distance_km=self._parse_distance(distance),
                        time_left=time_left or "Unknown"
                    )
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.warning(f"Error extrayendo raid individual: {e}")
                    continue
            
        except Exception as e:
            logger.warning(f"No se pudieron extraer resultados: {e}")
        
        return results
    
    def _extract_text(self, element, selector: str) -> Optional[str]:
        """Extraer texto de un elemento usando selector"""
        try:
            sub_element = element.locator(selector).first
            if sub_element.is_visible():
                return sub_element.text_content().strip()
        except:
            pass
        return None
    
    def _parse_distance(self, distance_text: Optional[str]) -> float:
        """Parsear texto de distancia a número"""
        if not distance_text:
            return 0.0
        
        try:
            # Extraer número de texto como "2.5 km"
            import re
            match = re.search(r'(\d+\.?\d*)', distance_text)
            if match:
                return float(match.group(1))
        except:
            pass
        
        return 0.0

def main():
    parser = argparse.ArgumentParser(description='Buscador de raids en PGTools')
    parser.add_argument('--lat', type=float, required=True, help='Latitud')
    parser.add_argument('--lon', type=float, required=True, help='Longitud')
    parser.add_argument('--pokemon-id', type=int, help='ID del Pokémon específico')
    parser.add_argument('--raid-level', type=int, choices=[1,2,3,4,5], help='Nivel de raid')
    parser.add_argument('--max-distance', type=float, default=20.0, help='Distancia máxima en km')
    parser.add_argument('--headless', action='store_true', help='Ejecutar sin interfaz gráfica')
    parser.add_argument('--output-json', help='Guardar resultados en archivo JSON')
    parser.add_argument('--timeout', type=int, default=30000, help='Timeout en milisegundos')
    
    args = parser.parse_args()
    
    # Validar coordenadas
    if not (-90 <= args.lat <= 90) or not (-180 <= args.lon <= 180):
        logger.error("Coordenadas inválidas")
        sys.exit(1)
    
    # Crear buscador
    finder = PGToolsRaidFinder(headless=args.headless, timeout=args.timeout)
    
    # Buscar raids
    results = finder.search_raids(
        args.lat, args.lon,
        args.pokemon_id, args.raid_level,
        args.max_distance
    )
    
    if not results:
        print("❌ No se encontraron raids")
        return
    
    # Mostrar resultados
    print(f"\n🎯 Encontrados {len(results)} raids:")
    print("=" * 80)
    
    for i, raid in enumerate(results, 1):
        print(f"{i:2d}. 🐾 {raid.pokemon_name}")
        print(f"    🏛️  {raid.gym_name}")
        print(f"    📍 {raid.latitude:.6f}, {raid.longitude:.6f}")
        print(f"    📏 {raid.distance_km:.2f} km")
        print(f"    ⏰ {raid.time_left}")
        print()
    
    # Guardar en JSON si se especifica
    if args.output_json:
        results_data = []
        for raid in results:
            results_data.append({
                'pokemon_name': raid.pokemon_name,
                'pokemon_id': raid.pokemon_id,
                'raid_level': raid.raid_level,
                'gym_name': raid.gym_name,
                'latitude': raid.latitude,
                'longitude': raid.longitude,
                'distance_km': raid.distance_km,
                'time_left': raid.time_left
            })
        
        with open(args.output_json, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        print(f"💾 Resultados guardados en: {args.output_json}")

if __name__ == "__main__":
    main()
