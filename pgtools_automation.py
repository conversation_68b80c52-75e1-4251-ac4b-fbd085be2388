import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    page.goto("https://pgtools.net/raids")
    page.get_by_role("button", name="right Configs and Filters").click()
    page.get_by_role("textbox", name="Enter your coordinates").click()
    page.get_by_role("textbox", name="Enter your coordinates").fill("40.745276,-74.009244")
    page.get_by_role("textbox", name="Enter your coordinates").press("Enter")
    page.goto("https://pgtools.net/raids")
    page.close()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
